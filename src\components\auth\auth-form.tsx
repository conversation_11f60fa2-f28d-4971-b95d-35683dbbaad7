"use client";

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import LoginForm from "./login-form";
import SignupForm from "./signup-form";

export default function AuthForm() {
  return (
    <Card className="w-full max-w-md border-2 border-black rounded-lg shadow-[8px_8px_0px_#000]">
      <CardHeader className="text-center">
        <CardTitle className="text-2xl font-headline">Welcome to POLITICA</CardTitle>
        <CardDescription>
            The Professional Network for Structured Intellectual Exchange.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="login" className="w-full">
          <TabsList className="grid w-full grid-cols-2 border-2 border-black h-12">
            <TabsTrigger value="login" className="text-base font-bold h-full data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">Sign In</TabsTrigger>
            <TabsTrigger value="signup" className="text-base font-bold h-full data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">Sign Up</TabsTrigger>
          </TabsList>
          <TabsContent value="login" className="pt-6">
            <LoginForm />
          </TabsContent>
          <TabsContent value="signup" className="pt-6">
            <SignupForm />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
