
'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Loader2, Send } from 'lucide-react';
import { createPostAction } from '@/app/actions';
import { useToast } from '@/hooks/use-toast';
import { useState } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from './ui/form';
import MediaUpload from './media-upload';
import type { MediaItem } from '@/types/post';

const postSchema = z.object({
  content: z.string().min(10, 'Post must be at least 10 characters long.').max(500, 'Post cannot exceed 500 characters.'),
  space: z.string().min(1, 'Please select a space for your post.'),
  media: z.array(z.object({
    id: z.string(),
    url: z.string(),
    fileName: z.string(),
    fileSize: z.number(),
    originalSize: z.number(),
    mimeType: z.string(),
    width: z.number().optional(),
    height: z.number().optional(),
  })).optional(),
});

type PostFormValues = z.infer<typeof postSchema>;

function SubmitButton({ isPending }: { isPending: boolean }) {
  return (
    <Button type="submit" disabled={isPending} className="bg-accent text-accent-foreground border-2 border-black shadow-[2px_2px_0px_#000]">
      {isPending ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Send className="mr-2 h-4 w-4" />}
      {isPending ? 'Posting...' : 'Submit Post'}
    </Button>
  );
}

export default function CreatePostForm({ setOpen }: { setOpen: (open: boolean) => void }) {
  const { toast } = useToast();
  const [isPending, setIsPending] = useState(false);
  const [media, setMedia] = useState<MediaItem[]>([]);

  const form = useForm<PostFormValues>({
    resolver: zodResolver(postSchema),
    defaultValues: {
      content: '',
      space: '',
      media: [],
    },
  });

  const onSubmit = async (data: PostFormValues) => {
    setIsPending(true);
    const formData = new FormData();
    formData.append('content', data.content);
    formData.append('space', data.space);
    if (media.length > 0) {
      formData.append('media', JSON.stringify(media));
    }

    try {
      const result = await createPostAction(null, formData);
      if (result.message === 'success') {
        toast({ title: 'Post Created!', description: 'Your post is now live on the feed.' });
        form.reset();
        setMedia([]);
        setOpen(false);
      } else {
        toast({
          variant: 'destructive',
          title: 'Error creating post',
          description: result.message || result.errors?.content?.[0] || result.errors?.space?.[0] || 'An unknown error occurred.',
        });
      }
    } catch (error) {
      toast({
        variant: 'destructive',
        title: 'Error creating post',
        description: 'An unexpected error occurred.',
      });
    } finally {
      setIsPending(false);
    }
  };
  
  return (
    <Form {...form}>
        <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="space-y-6"
        >
            <FormField
                control={form.control}
                name="content"
                render={({ field }) => (
                    <FormItem>
                        <FormLabel>Your Message</FormLabel>
                        <FormControl>
                            <Textarea
                                {...field}
                                placeholder="What's on your mind? Share an idea, ask a question, or start a debate."
                                className="min-h-[150px] border-2 border-black"
                            />
                        </FormControl>
                        <FormMessage />
                    </FormItem>
                )}
            />

            <FormField
                control={form.control}
                name="space"
                render={({ field }) => (
                    <FormItem>
                        <FormLabel>Choose a Space</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                                <SelectTrigger className="border-2 border-black">
                                    <SelectValue placeholder="Select a relevant space for your post" />
                                </SelectTrigger>
                            </FormControl>
                            <SelectContent className="border-2 border-black">
                                <SelectItem value="Professional">Professional</SelectItem>
                                <SelectItem value="Politics">Politics</SelectItem>
                                <SelectItem value="Science">Science</SelectItem>
                                <SelectItem value="Forum">Forum</SelectItem>
                            </SelectContent>
                        </Select>
                        <FormMessage />
                    </FormItem>
                )}
            />

            <div className="space-y-2">
                <label className="text-sm font-medium">Media Attachments (Optional)</label>
                <MediaUpload
                    onMediaChange={setMedia}
                    maxFiles={4}
                    disabled={isPending}
                />
            </div>

            <SubmitButton isPending={isPending} />
        </form>
    </Form>
  );
}
