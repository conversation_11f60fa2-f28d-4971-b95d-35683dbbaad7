'use server';

/**
 * @fileOverview An AI agent that can explain topics within a discussion thread.
 *
 * - genesisExplainer - A function that generates an explanation based on a user's question and thread context.
 * - GenesisExplainerInput - The input type for the genesisExplainer function.
 * - GenesisExplainerOutput - The return type for the genesisExplainer function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const GenesisExplainerInputSchema = z.object({
  userQuestion: z.string().describe('The question asked by the user.'),
  threadContext: z.string().describe('The content of the parent post or comment for context.'),
});
export type GenesisExplainerInput = z.infer<typeof GenesisExplainerInputSchema>;

const GenesisExplainerOutputSchema = z.object({
  explanation: z.string().describe('The detailed explanation generated by the AI.'),
});
export type GenesisExplainerOutput = z.infer<typeof GenesisExplainerOutputSchema>;

export async function genesisExplainer(input: GenesisExplainerInput): Promise<GenesisExplainerOutput> {
  return genesisExplainerFlow(input);
}

const prompt = ai.definePrompt({
  name: 'genesisExplainerPrompt',
  input: {schema: GenesisExplainerInputSchema},
  output: {schema: GenesisExplainerOutputSchema},
  prompt: `You are Genesis, an expert AI assistant integrated into the POLITICA professional network. Your role is to provide clear, detailed, and objective explanations on topics discussed within the platform.

A user has mentioned you in a comment to ask a question. Use the provided thread context to understand what is being discussed.

Thread Context:
---
{{{threadContext}}}
---

User's Question:
---
"{{{userQuestion}}}"
---

Your Task:
1.  Analyze the user's question and the thread context.
2.  Provide a comprehensive and well-structured explanation that directly addresses the user's question.
3.  If the question is about the context, summarize or clarify it as requested.
4.  If the question is more general, answer it based on your expert knowledge, relating it back to the context if possible.
5.  Your response will be posted as a reply in the discussion thread. Maintain a helpful, formal, and informative tone.

Generate the explanation now.
  `,
});

const genesisExplainerFlow = ai.defineFlow(
  {
    name: 'genesisExplainerFlow',
    inputSchema: GenesisExplainerInputSchema,
    outputSchema: GenesisExplainerOutputSchema,
  },
  async input => {
    const {output} = await prompt(input);
    return output!;
  }
);
