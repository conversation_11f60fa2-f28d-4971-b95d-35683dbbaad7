
export interface Space {
  id: string;
  name: string;
  description: string;
  category: string;
  tags: string[];
  contentTypes: {
    text: boolean;
    documents: boolean;
    images: boolean;
    video: boolean;
    data: boolean;
    code: boolean;
    claims: boolean;
    polls: boolean;
  };
  discussionFormat: 'threaded' | 'linear' | 'q&a' | 'debate';
  contentApproval: boolean;
  versioning: boolean;
  tools: {
    projectManagement: boolean;
    fileStorage: boolean;
    codeSandbox: boolean;
    whiteboard: boolean;
    conferencing: boolean;
    calendar: boolean;
  };
  visibility: 'public' | 'private' | 'secret';
  publicJoinOptions?: 'open' | 'request';
  privateJoinOptions?: 'request' | 'invite';
  secretJoinOptions?: 'invite' | 'link';
  requiredAuthLevel: string;
  creatorId: string;
  creatorName: string;
  creatorAvatar: string;
  createdAt: string;
  memberCount: number;
}
