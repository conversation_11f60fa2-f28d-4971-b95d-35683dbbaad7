rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {

    // Users can be read by anyone, but only updated by the owner.
    match /users/{userId} {
      allow read: if true;
      allow write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Posts and their subcollections
    match /posts/{postId} {
      allow read: if true;
      allow create: if request.auth != null;
      allow update, delete: if request.auth != null && resource.data.authorId == request.auth.uid;
      
      match /votes/{userId} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }
      
      match /saves/{userId} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }

      match /comments/{commentId} {
        allow read: if true;
        allow create: if request.auth != null;
        allow update, delete: if request.auth != null && resource.data.authorId == request.auth.uid;

        match /commentVotes/{userId} {
             allow read, write: if request.auth != null && request.auth.uid == userId;
        }

        match /commentSaves/{userId} {
            allow read, write: if request.auth != null && request.auth.uid == userId;
        }
      }
    }

    // Spaces collection
    match /spaces/{spaceId} {
      // Authenticated users can create spaces.
      allow create: if request.auth != null;
      
      // Allow reading a space if it is public. 
      // This secures queries: only queries for public spaces will succeed.
      allow read: if resource.data.visibility == 'public';

      // For now, allow any authenticated user to read non-public spaces.
      // This is a temporary rule to allow development of private spaces.
      // In a real app, this should check for membership.
      allow read: if request.auth != null && resource.data.visibility != 'public';

      // Only the creator can update or delete the space.
      allow write: if request.auth != null && request.auth.uid == request.resource.data.creatorId;
    }
    
    // Follow relationships
    match /follows/{followId} {
        allow read: if true;
        // The document ID is followerId_followingId
        allow write: if request.auth != null && request.auth.uid == followId.split('_')[0];
    }
  }
}
