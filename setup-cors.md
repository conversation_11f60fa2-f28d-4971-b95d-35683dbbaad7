# Firebase Storage CORS Setup Guide

## Prerequisites
1. Install Google Cloud SDK: https://cloud.google.com/sdk/docs/install
2. Authenticate with your Google account that has access to the Firebase project

## Steps to Configure CORS

### 1. Install Google Cloud SDK
Download and install from: https://cloud.google.com/sdk/docs/install

### 2. Authenticate with Google Cloud
```bash
gcloud auth login
```

### 3. Set your project ID
```bash
gcloud config set project politica-overview
```

### 4. Apply CORS configuration to Firebase Storage
```bash
gsutil cors set cors.json gs://politica-overview.firebasestorage.app
```

### 5. Verify CORS configuration
```bash
gsutil cors get gs://politica-overview.firebasestorage.app
```

## Alternative: Using Firebase CLI

If you prefer using Firebase CLI:

### 1. Install Firebase CLI (if not already installed)
```bash
npm install -g firebase-tools
```

### 2. Login to Firebase
```bash
firebase login
```

### 3. Set project
```bash
firebase use politica-overview
```

### 4. Deploy storage rules (this will also help with CORS)
```bash
firebase deploy --only storage
```

## Troubleshooting

If you still have CORS issues after configuration:

1. **Clear browser cache** - CORS policies are cached
2. **Wait 5-10 minutes** - Changes can take time to propagate
3. **Check Firebase Console** - Verify storage rules are deployed
4. **Test in incognito mode** - Eliminates cache issues

## Testing CORS Configuration

After applying CORS settings, test by:
1. Restarting your development server
2. Trying to upload an image
3. Checking browser network tab for successful OPTIONS requests
