'use client';
import { Badge } from "@/components/ui/badge";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { TrendingUp } from "lucide-react";

const trendingTopics = [
    { rank: 1, name: "#ClimatePolicy", posts: 847, change: "+15%" },
    { rank: 2, name: "#AIRegulation", posts: 632, change: "+28%" },
    { rank: 3, name: "#TechEthics", posts: 531, change: "+12%" },
];

export default function TrendingTopics() {
    return (
        <Card className="border-2 border-black rounded-lg shadow-[4px_4px_0px_#000]">
            <CardHeader>
                <CardTitle className="font-bold flex items-center gap-2">
                    <TrendingUp className="h-5 w-5" />
                    <span>Trending Topics</span>
                </CardTitle>
            </CardHeader>
            <CardContent>
                <ul className="space-y-4">
                    {trendingTopics.map((topic) => (
                        <li key={topic.rank} className="flex items-center gap-4">
                            <span className="text-lg font-bold text-muted-foreground">{`#${topic.rank}`}</span>
                            <div className="flex-1">
                                <p className="font-bold">{topic.name}</p>
                                <p className="text-xs text-muted-foreground">{topic.posts} posts</p>
                            </div>
                            <Badge className="border-2 border-black bg-green-100 text-green-800">{topic.change}</Badge>
                        </li>
                    ))}
                </ul>
            </CardContent>
        </Card>
    )
}
