
'use client';

import { useState, useEffect } from 'react';
import { format } from 'date-fns';
import Link from 'next/link';
import type { Post } from '@/types/post';
import type { Comment as CommentType } from '@/types/comment';
import type { UserProfile } from '@/types/user';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import FollowButton from "@/components/follow-button";
import AddCommentForm from "@/components/add-comment-form";
import Comment from "@/components/comment";
import { Repeat2, MessageCircle, Bookmark, Share2, Loader2, MessageSquareQuote } from 'lucide-react';
import { Card } from './ui/card';
import { cn } from '@/lib/utils';
import { Button } from './ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from './ui/dropdown-menu';
import { useToast } from '@/hooks/use-toast';
import { useTransition } from 'react';
import { saveAction, repostAction, repostCommentAction } from '@/app/actions';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from './ui/dialog';
import { Textarea } from './ui/textarea';
import { useRouter } from 'next/navigation';
import PostMedia from './post-media';

const buildCommentTree = (comments: CommentType[]): CommentType[] => {
    const tree: CommentType[] = [];
    const childrenOf = new Map<string, CommentType[]>();

    comments.forEach(comment => {
        const parentId = comment.parentCommentId || null;
        if (!childrenOf.has(parentId)) {
            childrenOf.set(parentId, []);
        }
        childrenOf.get(parentId)!.push(comment);
    });

    const getChildren = (id: string | null): CommentType[] => {
        const children = childrenOf.get(id) || [];
        children.forEach(child => {
            child.replies = getChildren(child.id);
        });
        return children.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
    };

    return getChildren(null); // Start from top-level comments
};

interface PostDetailProps {
    post: Post;
    flatComments: CommentType[];
    currentUserProfile: UserProfile | null;
    currentUserId: string | null;
}

function PostDetailActions({ post: initialPost, currentUserId }: { post: Post, currentUserId: string | null }) {
    const [post, setPost] = useState(initialPost);
    const [isSavePending, startSaveTransition] = useTransition();
    const [isRepostPending, startRepostTransition] = useTransition();
    const [isQuoteDialogOpen, setQuoteDialogOpen] = useState(false);
    const [quoteContent, setQuoteContent] = useState('');
    const { toast } = useToast();
    const router = useRouter();

    const displayData = post.isRepost ? post.originalPost! : post;
    const isCommentRepost = post.isRepost && post.originalComment;

    const handleAction = () => {
        router.refresh();
    }

    const handleSave = () => {
        if (!currentUserId) {
            toast({ variant: "destructive", title: "Please log in to save posts." });
            return;
        }
        startSaveTransition(async () => {
            const result = await saveAction(post.id, !post.isSaved);
            if (result.success) {
                handleAction();
            } else {
                toast({ variant: "destructive", title: "Action failed", description: result.message });
            }
        });
    };

    const handleShare = () => {
        const postUrl = `${window.location.origin}/post/${displayData.id}`;
        navigator.clipboard.writeText(postUrl);
        toast({ title: "Link Copied!", description: "Post URL has been copied to your clipboard." });
    };

    const handlePostRepost = () => {
        if (!currentUserId) {
            toast({ variant: "destructive", title: "Please log in to repost." });
            return;
        }
        startRepostTransition(async () => {
            const result = await repostAction(displayData.id);
            if (result.success) {
                toast({ title: "Post Reposted!" });
                handleAction();
            } else {
                toast({ variant: "destructive", title: "Repost Failed", description: result.message });
            }
        });
    }

    const handleCommentRepost = () => {
        if (!currentUserId) {
          toast({ variant: "destructive", title: "Please log in to repost." });
          return;
        }
        startRepostTransition(async () => {
          const comment = displayData as unknown as CommentType;
          const result = await repostCommentAction(comment.postId, comment.id);
          if (result.success) {
            toast({ title: "Comment Reposted!", description: "The comment has been reposted to the main feed." });
            handleAction();
          } else {
            toast({ variant: "destructive", title: "Repost Failed", description: result.message });
          }
        });
      }

    const handleQuoteSubmit = () => {
        if (!currentUserId) {
            toast({ variant: "destructive", title: "Please log in to repost." });
            return;
        }
        startRepostTransition(async () => {
            const result = isCommentRepost
                ? await repostCommentAction((displayData as unknown as CommentType).postId, displayData.id, quoteContent)
                : await repostAction(displayData.id, quoteContent);

            if (result.success) {
                toast({ title: "Post Quoted!", description: "You have successfully quoted the post." });
                setQuoteDialogOpen(false);
                setQuoteContent('');
                handleAction();
            } else {
                toast({ variant: "destructive", title: "Quote Failed", description: result.message });
            }
        });
    }

    const QuotedPostPreview = () => (
        <div className="mt-4 border-2 border-black rounded-lg p-3 space-y-2">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Avatar className="h-5 w-5 border-2 border-black">
                    <AvatarImage src={displayData.authorAvatar} alt={`@${displayData.authorName}`} />
                    <AvatarFallback>{displayData.authorName.substring(0, 1)}</AvatarFallback>
                </Avatar>
                <span className="font-bold text-foreground">{displayData.authorName}</span>
            </div>
            <p className="text-sm line-clamp-4">{displayData.content}</p>
        </div>
    );

    return (
        <>
            <div className="mt-4 flex items-center justify-around gap-2 text-sm text-muted-foreground border-2 border-black rounded-lg p-1">
                <Button variant="ghost" className="flex items-center gap-1 px-2 hover:bg-muted w-full justify-center">
                    <MessageCircle className="h-4 w-4" />
                    <span>{post.commentsCount}</span>
                </Button>

                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="flex items-center gap-1 px-2 hover:bg-muted w-full justify-center" disabled={isRepostPending || (post.isRepost && !post.content)}>
                            {isRepostPending ? <Loader2 className="h-4 w-4 animate-spin" /> : <Repeat2 className="h-4 w-4" />}
                            <span>Repost</span>
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="border-2 border-black">
                        <DropdownMenuItem onSelect={isCommentRepost ? handleCommentRepost : handlePostRepost} disabled={post.isRepost || isRepostPending}>
                            <Repeat2 className="mr-2 h-4 w-4" /> Repost
                        </DropdownMenuItem>
                        <DropdownMenuItem onSelect={() => setQuoteDialogOpen(true)} disabled={post.isRepost || isRepostPending}>
                            <MessageSquareQuote className="mr-2 h-4 w-4" /> Quote
                        </DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>

                <Button variant="ghost" className="flex items-center gap-1 px-2 hover:bg-muted w-full justify-center" onClick={handleSave} disabled={isSavePending}>
                    <Bookmark className={cn("h-4 w-4", post.isSaved && 'fill-current text-accent')} />
                    <span>{isSavePending ? '...' : (post.isSaved ? 'Saved' : 'Save')}</span>
                </Button>

                <Button variant="ghost" className="flex items-center gap-1 px-2 hover:bg-muted w-full justify-center" onClick={handleShare}>
                    <Share2 className="h-4 w-4" />
                    <span>Share</span>
                </Button>
            </div>
            <Dialog open={isQuoteDialogOpen} onOpenChange={setQuoteDialogOpen}>
                <DialogContent className="sm:max-w-[625px] border-2 border-black shadow-[8px_8px_0px_black] rounded-lg">
                    <DialogHeader>
                        <DialogTitle className="font-headline text-2xl">Quote Post</DialogTitle>
                        <DialogDescription>Add your own thoughts to this post.</DialogDescription>
                    </DialogHeader>
                    <div className="grid gap-4 py-4">
                        <Textarea
                            placeholder="Add a comment..."
                            value={quoteContent}
                            onChange={(e) => setQuoteContent(e.target.value)}
                            className="min-h-[100px] border-2 border-black"
                        />
                        <QuotedPostPreview />
                    </div>
                    <div className="flex justify-end">
                        <Button onClick={handleQuoteSubmit} disabled={isRepostPending || quoteContent.length === 0} className="bg-accent text-accent-foreground border-2 border-black shadow-[2px_2px_0px_#000]">
                            {isRepostPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                            Post
                        </Button>
                    </div>
                </DialogContent>
            </Dialog>
        </>
    );
}

export default function PostDetail({ post, flatComments, currentUserProfile, currentUserId }: PostDetailProps) {
    const [isMounted, setIsMounted] = useState(false);
    useEffect(() => {
        setIsMounted(true);
    }, []);

    const commentTree = buildCommentTree(flatComments);
    const displayPost = post.isRepost ? (post.originalPost || post.originalComment)! : post;

    return (
        <main className="flex-1 bg-background">
            <div className="p-4 md:p-6 lg:p-8 space-y-8">
                {post.isRepost && (
                    <div className="text-sm text-muted-foreground flex items-center gap-2 px-4">
                        <Repeat2 className="h-4 w-4" />
                        <span>{post.authorName} reposted</span>
                    </div>
                )}
                <Card className={cn("border-2 border-black rounded-lg shadow-[4px_4px_0px_black] transition-transform duration-300 hover:scale-[1.01] hover:shadow-[8px_8px_0px_black]")}>
                    <div className="flex flex-col p-4">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                                <Avatar className="h-12 w-12 border-2 border-black">
                                    <AvatarImage src={displayPost.authorAvatar} alt={displayPost.authorName}/>
                                    <AvatarFallback>{displayPost.authorName.substring(0, 2).toUpperCase()}</AvatarFallback>
                                </Avatar>
                                <div>
                                    <p className="font-bold">{displayPost.authorName}</p>
                                    <p className="text-sm text-muted-foreground">@{displayPost.authorName.toLowerCase().replace(/\s/g, '')}</p>
                                </div>
                            </div>
                            {currentUserId && (
                                <FollowButton 
                                    isFollowed={!!('isFollowed' in displayPost ? displayPost.isFollowed : false)}
                                    targetUserId={displayPost.authorId}
                                    currentUserId={currentUserId}
                                />
                            )}
                        </div>

                        <div className="py-6 space-y-4">
                            <p className="text-xl whitespace-pre-wrap leading-relaxed">{displayPost.content}</p>

                            {/* Display media if available */}
                            {'media' in displayPost && displayPost.media && displayPost.media.length > 0 && (
                                <PostMedia media={displayPost.media} />
                            )}

                            <p className="text-sm text-muted-foreground">
                                {isMounted ? format(new Date(displayPost.createdAt), "h:mm a · MMM d, yyyy") : <span className="h-4 w-48 bg-muted rounded-md inline-block animate-pulse" />}
                            </p>
                        </div>

                        <Separator className="bg-border/50" />
                        
                        {(displayPost.votes > 0 || displayPost.commentsCount > 0) && (
                            <>
                                <div className="flex gap-4 py-3 text-sm text-muted-foreground">
                                    {displayPost.votes > 0 && <span className="font-bold text-foreground">{displayPost.votes} <span className="font-normal text-muted-foreground">Likes</span></span>}
                                    {post.commentsCount > 0 && <span className="font-bold text-foreground">{post.commentsCount} <span className="font-normal text-muted-foreground">Replies</span></span>}
                                </div>
                                <Separator className="bg-border/50" />
                            </>
                        )}
                        
                        <PostDetailActions post={post} currentUserId={currentUserId} />
                    </div>
                </Card>

                {currentUserProfile ? (
                    <div className="flex gap-4 pt-6 border-t-2 border-black">
                        <Avatar className="h-10 w-10 border-2 border-black">
                            <AvatarImage src={currentUserProfile.avatarUrl} alt={currentUserProfile.name}/>
                            <AvatarFallback>{currentUserProfile.name.substring(0, 2).toUpperCase()}</AvatarFallback>
                        </Avatar>
                        <div className="flex-1">
                            <AddCommentForm postId={post.id} />
                        </div>
                    </div>
                ) : (
                    <div className="py-4 text-muted-foreground">
                        <Link href="/" className="text-accent hover:underline">Log in</Link> to post a reply.
                    </div>
                )}
                
                {commentTree.length > 0 && (
                    <div className="pt-8 space-y-8 border-t-2 border-black">
                        <h2 className="text-xl font-bold font-headline">Replies</h2>
                        {commentTree.map(comment => (
                            <Comment 
                                key={comment.id} 
                                comment={comment} 
                                currentUserId={currentUserId} 
                                currentUserProfile={currentUserProfile}
                            />
                        ))}
                    </div>
                )}
            </div>
        </main>
    );
}
