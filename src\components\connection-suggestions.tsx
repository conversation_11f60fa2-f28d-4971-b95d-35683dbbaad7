'use client';

import { useEffect, useState } from "react";
import { aiConnectionSuggestions, type AiConnectionSuggestionsOutput } from "@/ai/flows/ai-connection-suggestions";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { User<PERSON><PERSON>, Bo<PERSON> } from "lucide-react";
import { Skeleton } from "./ui/skeleton";

function ConnectionSuggestionsSkeleton() {
    return (
        <Card className="border-2 border-black shadow-[4px_4px_0px_black] rounded-lg">
            <CardHeader>
                <CardTitle className="font-bold flex items-center gap-2">
                    <Bot className="h-5 w-5"/>
                    <span>AI Connections</span>
                </CardTitle>
                <CardDescription>
                AI-powered recommendations to expand your network.
                </CardDescription>
            </CardHeader>
            <CardContent className="grid gap-3">
                {[...Array(3)].map((_, i) => (
                    <div key={i} className="flex items-start gap-3 p-2">
                        <Skeleton className="h-10 w-10 rounded-full border-2 border-black" />
                        <div className="flex-1 space-y-2">
                            <Skeleton className="h-4 w-1/2" />
                            <Skeleton className="h-3 w-full" />
                        </div>
                        <Skeleton className="h-9 w-9 rounded-lg" />
                    </div>
                ))}
            </CardContent>
        </Card>
    );
}

export default function ConnectionSuggestions() {
  const [suggestions, setSuggestions] = useState<AiConnectionSuggestionsOutput | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchSuggestions() {
      try {
        const result = await aiConnectionSuggestions({
          userProfile: "Senior Policy Advisor with expertise in renewable energy and international climate agreements. Skills in data analysis, public speaking, and legislative drafting.",
          interests: "Carbon capture technology, sustainable urban development, international relations.",
          recentActivity: "Commented on a debate about nuclear fusion. Published an article on the economic impacts of the Paris Agreement.",
        });
        setSuggestions(result);
      } catch (err) {
        console.error("Error fetching connection suggestions:", err);
        setError("Failed to load suggestions.");
      } finally {
        setIsLoading(false);
      }
    }

    fetchSuggestions();
  }, []);

  if (isLoading) {
    return <ConnectionSuggestionsSkeleton />;
  }

  if (error || !suggestions || !suggestions.suggestedConnections || suggestions.suggestedConnections.length === 0) {
    return null; 
  }

  return (
    <Card className="border-2 border-black shadow-[4px_4px_0px_black] rounded-lg">
      <CardHeader>
        <CardTitle className="font-bold flex items-center gap-2">
            <Bot className="h-5 w-5"/>
            <span>AI Connections</span>
        </CardTitle>
        <CardDescription>
          AI-powered recommendations to expand your network.
        </CardDescription>
      </CardHeader>
      <CardContent className="grid gap-3">
        {suggestions.suggestedConnections.slice(0, 3).map((conn) => (
          <div key={conn.userId} className="flex items-start gap-3 p-2 rounded-lg hover:bg-muted">
            <Avatar className="h-10 w-10 sm:flex border-2 border-black">
              <AvatarImage src={`https://placehold.co/100x100.png?text=${conn.name.charAt(0)}`} alt={conn.name} />
              <AvatarFallback>{conn.name.charAt(0)}</AvatarFallback>
            </Avatar>
            <div className="grid gap-0.5 flex-1">
              <p className="text-sm font-semibold leading-none">{conn.name}</p>
              <p className="text-xs text-muted-foreground">{conn.profileSummary}</p>
            </div>
            <Button aria-label="Connect" variant="outline" size="icon" className="ml-auto shrink-0 border-2 border-black shadow-[2px_2px_0px_black] hover:shadow-[3px_3px_0px_black] active:shadow-[1px_1px_0px_black] transition-all hover:bg-accent hover:text-accent-foreground h-9 w-9">
              <UserPlus className="h-4 w-4" />
            </Button>
          </div>
        ))}
      </CardContent>
    </Card>
  );
}
