
'use client';

import { 
    <PERSON>Cir<PERSON>,
    Repeat2,
    Heart,
    Bookmark,
    Share2,
    Loader2
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useState, useTransition } from "react";
import { commentVoteAction, commentSaveAction, repostCommentAction } from "@/app/actions";
import { useToast } from "@/hooks/use-toast";
import type { Comment } from "@/types/comment";
import { cn } from "@/lib/utils";

export default function CommentActions({ comment: initialComment, currentUserId, onReplyClick }: { comment: Comment, currentUserId: string | null, onReplyClick: () => void }) {
    const [comment, setComment] = useState(initialComment);
    const [isVotePending, startVoteTransition] = useTransition();
    const [isSavePending, startSaveTransition] = useTransition();
    const [isRepostPending, startRepostTransition] = useTransition();
    const { toast } = useToast();

    const handleLike = (e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();
        if (!currentUserId) {
            toast({ variant: "destructive", title: "Please log in to like comments." });
            return;
        }
        startVoteTransition(async () => {
            const isLiked = comment.userVote === 'up';
            const originalVotes = comment.votes;
            
            const optimisticComment = { 
                ...comment, 
                userVote: isLiked ? null : 'up',
                votes: originalVotes + (isLiked ? -1 : 1)
            };
            setComment(optimisticComment);

            const result = await commentVoteAction(comment.postId, comment.id, 'up');
            if (!result.success) {
                setComment(initialComment); // Revert on failure
                toast({ variant: "destructive", title: "Action failed", description: result.message });
            }
        });
    };

    const handleSave = (e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();
        if (!currentUserId) {
            toast({ variant: "destructive", title: "Please log in to save comments." });
            return;
        }
        startSaveTransition(async () => {
            const optimisticSaved = !comment.isSaved;
            const optimisticComment = { ...comment, isSaved: optimisticSaved };
            setComment(optimisticComment);
            
            const result = await commentSaveAction(comment.postId, comment.id, optimisticSaved);
            if (!result.success) {
                setComment(initialComment); // Revert on failure
                toast({ variant: "destructive", title: "Action failed", description: result.message });
            }
        });
    };

    const handleRepost = (e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();
        if (!currentUserId) {
            toast({ variant: "destructive", title: "Please log in to repost." });
            return;
        }
        startRepostTransition(async () => {
            const result = await repostCommentAction(initialComment.postId, initialComment.id);
            if (result.success) {
                toast({ title: "Comment Reposted!", description: "The comment has been reposted to the main feed." });
            } else {
                toast({ variant: "destructive", title: "Repost Failed", description: result.message });
            }
        });
    };
  
    const handleShare = (e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();
        const commentUrl = `${window.location.origin}/post/${comment.postId}/comment/${comment.id}`;
        navigator.clipboard.writeText(commentUrl);
        toast({ title: "Link Copied!", description: "Link to comment has been copied to your clipboard." });
    };

    const handleReply = (e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();
        onReplyClick();
    }

    return (
        <div className="flex justify-start items-center -ml-2 mt-3 text-muted-foreground gap-1">
            <Button variant="ghost" size="icon" onClick={handleReply} className="group/action rounded-full hover:bg-blue-500/10 flex items-center gap-1.5 p-1.5">
                <MessageCircle className="h-5 w-5 text-muted-foreground group-hover/action:text-blue-500" />
                {comment.commentsCount > 0 && <span className="text-sm text-muted-foreground group-hover/action:text-blue-500">{comment.commentsCount}</span>}
            </Button>
            
            <Button variant="ghost" size="icon" onClick={handleRepost} disabled={isRepostPending} className="group/action rounded-full hover:bg-green-500/10 flex items-center gap-1">
                {isRepostPending ? <Loader2 className="h-5 w-5 animate-spin" /> : <Repeat2 className="h-5 w-5 text-muted-foreground group-hover/action:text-green-500" />}
            </Button>

            <Button variant="ghost" size="icon" onClick={handleLike} disabled={isVotePending} className="group/action rounded-full hover:bg-red-500/10 flex items-center gap-1">
                <Heart className={cn("h-5 w-5 text-muted-foreground group-hover/action:text-red-500", comment.userVote === 'up' && 'text-red-500 fill-current')} />
                {comment.votes > 0 && <span className={cn("text-sm group-hover/action:text-red-500", comment.userVote === 'up' && 'text-red-500')}>{comment.votes}</span>}
            </Button>

            <Button variant="ghost" size="icon" onClick={handleSave} disabled={isSavePending} className="group/action rounded-full hover:bg-yellow-500/10">
                <Bookmark className={cn("h-5 w-5 text-muted-foreground group-hover/action:text-yellow-500", comment.isSaved && 'text-yellow-500 fill-current')} />
            </Button>

             <Button variant="ghost" size="icon" onClick={handleShare} className="group/action rounded-full hover:bg-blue-500/10">
                <Share2 className="h-5 w-5 text-muted-foreground group-hover/action:text-blue-500" />
            </Button>
        </div>
    );
}
