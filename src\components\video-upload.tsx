'use client';

import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { 
  Upload, 
  Video, 
  X, 
  Play, 
  Pause, 
  Volume2, 
  VolumeX,
  Loader2,
  AlertCircle,
  CheckCircle,
  Clock,
  HardDrive
} from 'lucide-react';
import { 
  compressVideo, 
  formatVideoDuration, 
  formatVideoSize,
  isValidVideoFile,
  type VideoCompressionResult,
  type VideoCompressionProgress 
} from '@/lib/video-processing';
import { 
  uploadVideo, 
  type UploadProgress 
} from '@/lib/firebase-storage';
import type { MediaItem } from '@/types/post';

interface VideoUploadProps {
  onVideoChange: (video: MediaItem | null) => void;
  disabled?: boolean;
  className?: string;
}

interface VideoState {
  id: string;
  file: File;
  preview: string;
  status: 'pending' | 'compressing' | 'uploading' | 'complete' | 'error';
  compressionProgress: number;
  uploadProgress: number;
  error?: string;
  compressionResult?: VideoCompressionResult;
  mediaItem?: MediaItem;
}

export default function VideoUpload({ 
  onVideoChange, 
  disabled = false,
  className 
}: VideoUploadProps) {
  const [video, setVideo] = useState<VideoState | null>(null);
  const [isDragOver, setIsDragOver] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(true);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const videoRef = useRef<HTMLVideoElement>(null);

  // Effect to notify parent when video changes
  useEffect(() => {
    if (video?.status === 'complete' && video.mediaItem) {
      onVideoChange(video.mediaItem);
    } else {
      onVideoChange(null);
    }
  }, [video, onVideoChange]);

  const updateVideoState = useCallback((updates: Partial<VideoState>) => {
    setVideo(prev => prev ? { ...prev, ...updates } : null);
  }, []);

  const removeVideo = useCallback(() => {
    if (video?.preview) {
      URL.revokeObjectURL(video.preview);
    }
    setVideo(null);
  }, [video]);

  const processVideo = useCallback(async (file: File) => {
    const id = Math.random().toString(36).substring(2);
    const preview = URL.createObjectURL(file);
    
    const newVideo: VideoState = {
      id,
      file,
      preview,
      status: 'pending',
      compressionProgress: 0,
      uploadProgress: 0,
    };
    
    setVideo(newVideo);

    try {
      // Compress video
      updateVideoState({ status: 'compressing' });
      
      const compressionResult = await compressVideo(
        file,
        {
          maxSizeMB: 100, // 100MB max for videos
          maxDurationSeconds: 600, // 10 minutes max
          quality: 'medium'
        },
        (progress: VideoCompressionProgress) => {
          updateVideoState({ 
            compressionProgress: progress.progress,
            error: progress.error 
          });
        }
      );

      updateVideoState({
        status: 'uploading',
        compressionResult,
        compressionProgress: 100
      });

      // Upload video
      const uploadResult = await uploadVideo(
        compressionResult.compressedFile,
        file.size,
        {
          width: compressionResult.width,
          height: compressionResult.height,
          duration: compressionResult.duration,
          frameRate: compressionResult.frameRate
        },
        compressionResult.thumbnail,
        (progress: UploadProgress) => {
          updateVideoState({ uploadProgress: progress.progress });
        }
      );

      const mediaItem = uploadResult.mediaItem!;

      updateVideoState({
        status: 'complete',
        uploadProgress: 100,
        mediaItem
      });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Processing failed';
      updateVideoState({
        status: 'error',
        error: errorMessage
      });
    }
  }, [updateVideoState]);

  const handleFileSelect = useCallback((selectedFiles: FileList | null) => {
    if (!selectedFiles || disabled || selectedFiles.length === 0) return;

    const file = selectedFiles[0];
    
    if (!isValidVideoFile(file)) {
      alert('Please select a valid video file (MP4, WebM, MOV, or AVI)');
      return;
    }

    // Check file size (100MB limit)
    const maxSize = 100 * 1024 * 1024; // 100MB
    if (file.size > maxSize) {
      alert('Video file is too large. Maximum size is 100MB.');
      return;
    }

    processVideo(file);
  }, [disabled, processVideo]);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    handleFileSelect(e.dataTransfer.files);
  }, [handleFileSelect]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const togglePlayPause = useCallback(() => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  }, [isPlaying]);

  const toggleMute = useCallback(() => {
    if (videoRef.current) {
      videoRef.current.muted = !isMuted;
      setIsMuted(!isMuted);
    }
  }, [isMuted]);

  if (video) {
    return (
      <Card className={`border-2 border-black ${className}`}>
        <CardContent className="p-4">
          <div className="space-y-4">
            {/* Video Preview */}
            <div className="relative bg-black rounded-lg overflow-hidden">
              <video
                ref={videoRef}
                src={video.preview}
                className="w-full h-48 object-contain"
                muted={isMuted}
                onPlay={() => setIsPlaying(true)}
                onPause={() => setIsPlaying(false)}
                onEnded={() => setIsPlaying(false)}
              />
              
              {/* Video Controls Overlay */}
              <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center">
                <div className="flex items-center gap-2">
                  <Button
                    variant="secondary"
                    size="sm"
                    onClick={togglePlayPause}
                    className="bg-black bg-opacity-50 hover:bg-opacity-70"
                  >
                    {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                  </Button>
                  <Button
                    variant="secondary"
                    size="sm"
                    onClick={toggleMute}
                    className="bg-black bg-opacity-50 hover:bg-opacity-70"
                  >
                    {isMuted ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
                  </Button>
                </div>
              </div>

              {/* Remove Button */}
              <Button
                variant="destructive"
                size="sm"
                onClick={removeVideo}
                className="absolute top-2 right-2"
                disabled={video.status === 'compressing' || video.status === 'uploading'}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            {/* Video Info */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="font-medium truncate">{video.file.name}</span>
                <Badge variant={
                  video.status === 'complete' ? 'default' :
                  video.status === 'error' ? 'destructive' :
                  'secondary'
                }>
                  {video.status === 'pending' && <Loader2 className="h-3 w-3 mr-1 animate-spin" />}
                  {video.status === 'compressing' && <Loader2 className="h-3 w-3 mr-1 animate-spin" />}
                  {video.status === 'uploading' && <Loader2 className="h-3 w-3 mr-1 animate-spin" />}
                  {video.status === 'complete' && <CheckCircle className="h-3 w-3 mr-1" />}
                  {video.status === 'error' && <AlertCircle className="h-3 w-3 mr-1" />}
                  {video.status.charAt(0).toUpperCase() + video.status.slice(1)}
                </Badge>
              </div>

              <div className="flex items-center gap-4 text-sm text-gray-600">
                <div className="flex items-center gap-1">
                  <HardDrive className="h-3 w-3" />
                  {formatVideoSize(video.file.size)}
                </div>
                {video.compressionResult && (
                  <>
                    <div className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      {formatVideoDuration(video.compressionResult.duration)}
                    </div>
                    <div>
                      {video.compressionResult.width}×{video.compressionResult.height}
                    </div>
                  </>
                )}
              </div>

              {/* Progress Bars */}
              {video.status === 'compressing' && (
                <div className="space-y-1">
                  <div className="flex justify-between text-sm">
                    <span>Compressing video...</span>
                    <span>{Math.round(video.compressionProgress)}%</span>
                  </div>
                  <Progress value={video.compressionProgress} className="h-2" />
                </div>
              )}

              {video.status === 'uploading' && (
                <div className="space-y-1">
                  <div className="flex justify-between text-sm">
                    <span>Uploading video...</span>
                    <span>{Math.round(video.uploadProgress)}%</span>
                  </div>
                  <Progress value={video.uploadProgress} className="h-2" />
                </div>
              )}

              {video.status === 'error' && video.error && (
                <div className="text-red-600 text-sm bg-red-50 p-2 rounded">
                  {video.error}
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div
      className={`border-2 border-dashed border-gray-300 rounded-lg p-8 text-center transition-colors ${
        isDragOver ? 'border-blue-500 bg-blue-50' : 'hover:border-gray-400'
      } ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'} ${className}`}
      onDrop={handleDrop}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onClick={() => !disabled && fileInputRef.current?.click()}
    >
      <input
        ref={fileInputRef}
        type="file"
        accept="video/*"
        onChange={(e) => handleFileSelect(e.target.files)}
        className="hidden"
        disabled={disabled}
      />
      
      <div className="space-y-4">
        <div className="mx-auto w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center">
          <Video className="h-6 w-6 text-gray-600" />
        </div>
        
        <div>
          <p className="text-lg font-medium">Upload Video</p>
          <p className="text-sm text-gray-600 mt-1">
            Drag and drop a video file here, or click to browse
          </p>
          <p className="text-xs text-gray-500 mt-2">
            Supports MP4, WebM, MOV, AVI • Max 100MB • Max 10 minutes
          </p>
        </div>
        
        <Button variant="outline" disabled={disabled}>
          <Upload className="h-4 w-4 mr-2" />
          Choose Video
        </Button>
      </div>
    </div>
  );
}
