
'use client';
import AppSidebar from "@/components/app-sidebar";
import { ScrollProvider, useScrollContext } from '@/context/scroll-context';
import { useEffect, useRef } from "react";

function LayoutWithScroll({ children }: { children: React.ReactNode }) {
    const { setIsScrolled } = useScrollContext();
    const scrollRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        const handleScroll = () => {
            if (scrollRef.current) {
                const { scrollTop } = scrollRef.current;
                setIsScrolled(scrollTop > 50); // Scroll threshold
            }
        };

        const scrollElement = scrollRef.current;
        if (scrollElement) {
            scrollElement.addEventListener('scroll', handleScroll);
        }

        return () => {
            if (scrollElement) {
                scrollElement.removeEventListener('scroll', handleScroll);
            }
        };
    }, [setIsScrolled]);

    return (
        <div className="grid h-screen w-full md:grid-cols-[auto_1fr]" suppressHydrationWarning>
            <div className="hidden md:block">
                <AppSidebar />
            </div>
            <div ref={scrollRef} className="flex flex-col overflow-y-auto">
                {children}
            </div>
        </div>
    );
}

export default function PageLayout({ children }: { children: React.ReactNode }) {
    return (
        <ScrollProvider>
            <LayoutWithScroll>{children}</LayoutWithScroll>
        </ScrollProvider>
    );
}
