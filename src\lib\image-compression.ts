import imageCompression from 'browser-image-compression';

export interface CompressionOptions {
  maxSizeMB?: number;
  maxWidthOrHeight?: number;
  useWebWorker?: boolean;
  quality?: number;
  initialQuality?: number;
}

export interface CompressionResult {
  compressedFile: File;
  originalSize: number;
  compressedSize: number;
  compressionRatio: number;
  width?: number;
  height?: number;
}

export interface CompressionProgress {
  progress: number;
  stage: 'compressing' | 'complete' | 'error';
  error?: string;
}

const DEFAULT_OPTIONS: CompressionOptions = {
  maxSizeMB: 1, // 1MB max file size
  maxWidthOrHeight: 1920, // Max dimension
  useWebWorker: true,
  quality: 0.8, // 80% quality
  initialQuality: 0.8,
};

/**
 * Compresses an image file with progress tracking
 */
export async function compressImage(
  file: File,
  options: CompressionOptions = {},
  onProgress?: (progress: CompressionProgress) => void
): Promise<CompressionResult> {
  const finalOptions = { ...DEFAULT_OPTIONS, ...options };
  
  try {
    // Validate file type
    if (!file.type.startsWith('image/')) {
      throw new Error('File must be an image');
    }

    // Get original dimensions
    const dimensions = await getImageDimensions(file);
    const originalSize = file.size;

    // Report compression start
    onProgress?.({
      progress: 0,
      stage: 'compressing'
    });

    // Compress the image
    const compressedFile = await imageCompression(file, {
      ...finalOptions,
      onProgress: (progress: number) => {
        onProgress?.({
          progress: Math.round(progress),
          stage: 'compressing'
        });
      }
    });

    const compressedSize = compressedFile.size;
    const compressionRatio = Math.round((1 - compressedSize / originalSize) * 100);

    // Report completion
    onProgress?.({
      progress: 100,
      stage: 'complete'
    });

    return {
      compressedFile,
      originalSize,
      compressedSize,
      compressionRatio,
      width: dimensions.width,
      height: dimensions.height
    };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Compression failed';
    
    onProgress?.({
      progress: 0,
      stage: 'error',
      error: errorMessage
    });

    throw new Error(`Image compression failed: ${errorMessage}`);
  }
}

/**
 * Compresses multiple images with progress tracking
 */
export async function compressImages(
  files: File[],
  options: CompressionOptions = {},
  onProgress?: (fileIndex: number, progress: CompressionProgress) => void
): Promise<CompressionResult[]> {
  const results: CompressionResult[] = [];
  
  for (let i = 0; i < files.length; i++) {
    const file = files[i];
    
    try {
      const result = await compressImage(
        file,
        options,
        (progress) => onProgress?.(i, progress)
      );
      results.push(result);
    } catch (error) {
      // Continue with other files even if one fails
      console.error(`Failed to compress ${file.name}:`, error);
      throw error; // Re-throw to let the caller handle it
    }
  }
  
  return results;
}

/**
 * Gets image dimensions from a file
 */
export function getImageDimensions(file: File): Promise<{ width: number; height: number }> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    const url = URL.createObjectURL(file);
    
    img.onload = () => {
      URL.revokeObjectURL(url);
      resolve({
        width: img.naturalWidth,
        height: img.naturalHeight
      });
    };
    
    img.onerror = () => {
      URL.revokeObjectURL(url);
      reject(new Error('Failed to load image'));
    };
    
    img.src = url;
  });
}

/**
 * Validates if a file is a supported image type
 */
export function isValidImageFile(file: File): boolean {
  const supportedTypes = [
    'image/jpeg',
    'image/jpg', 
    'image/png',
    'image/webp',
    'image/gif'
  ];
  
  return supportedTypes.includes(file.type.toLowerCase());
}

/**
 * Formats file size in human readable format
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Generates a unique filename with timestamp
 */
export function generateUniqueFileName(originalName: string): string {
  const timestamp = Date.now();
  const randomString = Math.random().toString(36).substring(2, 8);
  const extension = originalName.split('.').pop() || 'jpg';
  const nameWithoutExt = originalName.replace(/\.[^/.]+$/, '');
  
  return `${nameWithoutExt}_${timestamp}_${randomString}.${extension}`;
}
