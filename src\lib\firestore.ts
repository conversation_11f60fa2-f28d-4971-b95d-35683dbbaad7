'use server';

import { db, admin } from './firebase-admin';
import type { UserProfile } from '@/types/user';
import type { Post } from '@/types/post';
import type { Comment } from '@/types/comment';
import type { Space } from '@/types/space';

const USERS_COLLECTION = 'users';
const POSTS_COLLECTION = 'posts';
const FOLLOWS_COLLECTION = 'follows';
const COMMENTS_COLLECTION = 'comments';
const SPACES_COLLECTION = 'spaces';
const SAVES_COLLECTION = 'saves'; // Top-level saves collection

// Subcollection names
const VOTES_COLLECTION = 'votes';
// const SAVES_COLLECTION = 'saves'; // This is now a top-level collection
const COMMENT_VOTES_COLLECTION = 'commentVotes';
const COMMENT_SAVES_COLLECTION = 'commentSaves';


const genericDefaultProfile: UserProfile = {
  name: 'User Profile Not Found',
  title: 'Member',
  avatarUrl: 'https://placehold.co/200x200',
  reputation: 0,
  verification: 'Community',
  career: [],
  skills: [],
  achievements: [],
  followingCount: 0,
  followerCount: 0,
};

export async function getUserProfile(userId: string): Promise<UserProfile> {
  const userDoc = await db.collection(USERS_COLLECTION).doc(userId).get();

  if (!userDoc.exists) {
    console.warn(`User profile for ${userId} not found. Returning a generic default object. Profile should be created on signup.`);
    return genericDefaultProfile;
  }
  
  return userDoc.data() as UserProfile;
}

export async function updateUserProfile(userId: string, data: Partial<UserProfile>): Promise<void> {
  await db.collection(USERS_COLLECTION).doc(userId).set(data, { merge: true });
}

export async function initializeUserProfile(userId: string, data: { email: string, name?: string | null, avatarUrl?: string | null }): Promise<void> {
    const userRef = db.collection(USERS_COLLECTION).doc(userId);
    const userDoc = await userRef.get();

    if (!userDoc.exists) {
        const { email, name, avatarUrl } = data;
        const newProfile: UserProfile = {
            name: name || email.split('@')[0] || 'New User',
            title: 'New Member',
            avatarUrl: avatarUrl || `https://placehold.co/200x200.png?text=${(name || email)?.[0]?.toUpperCase()}`,
            reputation: 0,
            verification: 'Community',
            career: [],
            skills: [],
            achievements: [],
            followerCount: 0,
            followingCount: 0,
        };
        await userRef.set(newProfile);
    }
}


export async function createPost(postData: Omit<Post, 'id' | 'createdAt'>): Promise<string> {
  const postRef = await db.collection(POSTS_COLLECTION).add({
    ...postData,
    createdAt: admin.firestore.FieldValue.serverTimestamp(),
  });
  return postRef.id;
}

async function enrichPostsWithUserData(posts: Post[], userId?: string | null): Promise<Post[]> {
    if (!userId || posts.length === 0) {
        return posts.map(post => ({
            ...post,
            userVote: null,
            isSaved: false,
            isFollowed: false,
        }));
    }
    
    const postIds = posts.map(p => p.id);
    const authorIds = [...new Set(posts.map(p => p.isRepost && p.originalComment ? p.originalComment.authorId : p.authorId))];

    const userFollowingSnapshot = await db.collection(FOLLOWS_COLLECTION).where('followerId', '==', userId).where('followingId', 'in', authorIds).get();
    const userFollowing = new Set(userFollowingSnapshot.docs.map(doc => doc.data().followingId));

    const userSavesSnapshot = await db.collection(SAVES_COLLECTION).where('userId', '==', userId).where('postId', 'in', postIds).get();
    const userSavedPosts = new Set(userSavesSnapshot.docs.map(doc => doc.data().postId));

    const enrichedPosts = await Promise.all(posts.map(async (post) => {
        const voteRef = db.collection(POSTS_COLLECTION).doc(post.id).collection(VOTES_COLLECTION).doc(userId);

        const [voteDoc] = await Promise.all([voteRef.get()]);
        
        const authorIdToCheck = post.isRepost && post.originalComment ? post.originalComment.authorId : post.authorId;

        return {
            ...post,
            userVote: voteDoc.exists ? voteDoc.data()?.type : null,
            isSaved: userSavedPosts.has(post.id),
            isFollowed: userFollowing.has(authorIdToCheck),
        };
    }));

    return enrichedPosts;
}

export async function getPosts(space: string | null, userId?: string | null): Promise<Post[]> {
    const postsCollection = db.collection(POSTS_COLLECTION);
    let q;

    if (space) {
        q = postsCollection.where("space", "==", space).orderBy("createdAt", "desc").limit(20);
    } else {
        q = postsCollection.orderBy("createdAt", "desc").limit(20);
    }
    
    const snapshot = await q.get();

    if (snapshot.empty) {
        return [];
    }

    const posts = snapshot.docs.map(doc => {
        const data = doc.data();
        return {
            id: doc.id,
            ...data,
            createdAt: ((data.createdAt as admin.firestore.Timestamp)?.toDate() || new Date()).toISOString(),
        } as Post;
    });

    return enrichPostsWithUserData(posts, userId);
}


export async function getPost(postId: string, userId?: string | null): Promise<Post | null> {
    const doc = await db.collection(POSTS_COLLECTION).doc(postId).get();
    if (!doc.exists) {
        return null;
    }
    const data = doc.data()!;
    let post = { 
        id: doc.id, 
        ...data, 
        createdAt: ((data.createdAt as admin.firestore.Timestamp)?.toDate() || new Date()).toISOString() 
    } as Post;
    
    if (post.isRepost && post.originalPost) {
        post.originalPost.createdAt = ((post.originalPost.createdAt as unknown as admin.firestore.Timestamp)?.toDate() || new Date()).toISOString();
    }
     if (post.isRepost && post.originalComment) {
        post.originalComment.createdAt = ((post.originalComment.createdAt as unknown as admin.firestore.Timestamp)?.toDate() || new Date()).toISOString();
    }
    
    const postsToEnrich = post.isRepost && post.originalPost ? [post, post.originalPost] : [post];
    const enriched = await enrichPostsWithUserData(postsToEnrich, userId);

    const enrichedPost = enriched.find(p => p.id === post.id)!;
    if(post.isRepost && post.originalPost){
        enrichedPost.originalPost = enriched.find(p => p.id === post.originalPost!.id);
    }
    
    return enrichedPost;
}


export async function getRawPost(postId: string): Promise<Post | null> {
    const doc = await db.collection(POSTS_COLLECTION).doc(postId).get();
    if (!doc.exists) {
        return null;
    }
    const data = doc.data()!;
    
    if (data.isRepost && data.originalPost?.id) {
        return getRawPost(data.originalPost.id);
    }

    return {
        id: doc.id,
        content: data.content,
        space: data.space,
        authorId: data.authorId,
        authorName: data.authorName,
        authorAvatar: data.authorAvatar,
        createdAt: ((data.createdAt as admin.firestore.Timestamp)?.toDate() || new Date()).toISOString(),
        votes: data.votes,
        commentsCount: data.commentsCount,
    } as Post;
}

export async function getRawComment(postId: string, commentId: string): Promise<Comment | null> {
    const doc = await db.collection(POSTS_COLLECTION).doc(postId).collection(COMMENTS_COLLECTION).doc(commentId).get();
    if (!doc.exists) {
        return null;
    }
    const data = doc.data()!;
    return {
        id: doc.id,
        postId: postId,
        content: data.content,
        authorId: data.authorId,
        authorName: data.authorName,
        authorAvatar: data.authorAvatar,
        createdAt: ((data.createdAt as admin.firestore.Timestamp)?.toDate() || new Date()).toISOString(),
        votes: data.votes,
        commentsCount: data.commentsCount,
        parentCommentId: data.parentCommentId || null,
    } as Comment;
}

async function enrichCommentsWithUserData(comments: Comment[], userId: string | null | undefined, postId: string): Promise<Comment[]> {
    if (!userId || comments.length === 0) {
        return comments.map(comment => ({ ...comment, userVote: null, isSaved: false, replies: [] }));
    }

    const enrichedComments = await Promise.all(comments.map(async (comment) => {
        const voteRef = db.collection(POSTS_COLLECTION).doc(postId).collection(COMMENTS_COLLECTION).doc(comment.id).collection(COMMENT_VOTES_COLLECTION).doc(userId);
        const saveRef = db.collection(POSTS_COLLECTION).doc(postId).collection(COMMENTS_COLLECTION).doc(comment.id).collection(COMMENT_SAVES_COLLECTION).doc(userId);

        const [voteDoc, saveDoc] = await Promise.all([voteRef.get(), saveRef.get()]);

        return {
            ...comment,
            userVote: voteDoc.exists ? voteDoc.data()?.type : null,
            isSaved: saveDoc.exists,
            replies: [], // Initialize replies array
        };
    }));

    return enrichedComments;
}


export async function getComment(postId: string, commentId: string, userId?: string | null): Promise<Comment | null> {
    const doc = await db.collection(POSTS_COLLECTION).doc(postId).collection(COMMENTS_COLLECTION).doc(commentId).get();
    if (!doc.exists) {
        return null;
    }
    const data = doc.data()!;
    let comment = {
        id: doc.id,
        postId,
        ...data,
        createdAt: ((data.createdAt as admin.firestore.Timestamp)?.toDate() || new Date()).toISOString(),
    } as Comment;

    const enriched = await enrichCommentsWithUserData([comment], userId, postId);
    return enriched[0] || null;
}


export async function getComments(postId: string, userId?: string | null): Promise<Comment[]> {
  const snapshot = await db.collection(POSTS_COLLECTION).doc(postId).collection(COMMENTS_COLLECTION).orderBy('createdAt', 'asc').get();
  if (snapshot.empty) {
    return [];
  }
  const comments = snapshot.docs.map(doc => {
    const data = doc.data();
    return {
      id: doc.id,
      postId: postId,
      ...data,
      parentCommentId: data.parentCommentId || null,
      createdAt: ((data.createdAt as admin.firestore.Timestamp).toDate()).toISOString(),
    } as Comment;
  });

  return enrichCommentsWithUserData(comments, userId, postId);
}

export async function addComment(
    postId: string, 
    commentData: Omit<Comment, 'id' | 'createdAt' | 'postId' | 'votes' | 'commentsCount' | 'parentCommentId' | 'replies'>,
    parentCommentId: string | null
): Promise<string> {
    const postRef = db.collection(POSTS_COLLECTION).doc(postId);
    const postDocForId = await postRef.get();
    const targetPostId = postDocForId.data()?.isRepost ? postDocForId.data()?.originalPost.id : postId;
    const ultimatePostRef = db.collection(POSTS_COLLECTION).doc(targetPostId);
    
    const commentsCollectionRef = ultimatePostRef.collection(COMMENTS_COLLECTION);
    const newCommentRef = commentsCollectionRef.doc();

    await db.runTransaction(async (transaction) => {
        let parentRef: admin.firestore.DocumentReference;
        if (parentCommentId) {
            parentRef = commentsCollectionRef.doc(parentCommentId);
        } else {
            parentRef = ultimatePostRef;
        }

        const parentDoc = await transaction.get(parentRef);
        if (!parentDoc.exists) {
            throw new Error("Parent post or comment not found");
        }

        transaction.set(newCommentRef, {
            ...commentData,
            parentCommentId: parentCommentId,
            createdAt: admin.firestore.FieldValue.serverTimestamp(),
            votes: 0,
            commentsCount: 0,
        });

        transaction.update(parentRef, {
            commentsCount: admin.firestore.FieldValue.increment(1),
        });
        
        if (parentCommentId && parentRef.path !== ultimatePostRef.path) {
            transaction.update(ultimatePostRef, {
                commentsCount: admin.firestore.FieldValue.increment(1),
            });
        }
    });

    return newCommentRef.id;
}


export async function handleVote(userId: string, postId: string, voteType: 'up' | 'down') {
    const voteRef = db.collection(POSTS_COLLECTION).doc(postId).collection(VOTES_COLLECTION).doc(userId);
    const postRef = db.collection(POSTS_COLLECTION).doc(postId);

    return db.runTransaction(async (transaction) => {
        const voteDoc = await transaction.get(voteRef);
        const postDoc = await transaction.get(postRef);
        if (!postDoc.exists) throw new Error("Post not found");

        const postData = postDoc.data()!;
        let currentVotes = postData.votes || 0;
        let voteChange = 0;

        if (voteDoc.exists) {
            const existingVote = voteDoc.data()?.type;
            if (existingVote === voteType) {
                // User is un-voting
                transaction.delete(voteRef);
                voteChange = voteType === 'up' ? -1 : 1;
            } else {
                // User is changing their vote
                transaction.update(voteRef, { type: voteType });
                voteChange = voteType === 'up' ? 2 : -2;
            }
        } else {
            // New vote
            transaction.set(voteRef, { userId, postId, type: voteType, createdAt: admin.firestore.FieldValue.serverTimestamp() });
            voteChange = voteType === 'up' ? 1 : -1;
        }
        transaction.update(postRef, { votes: currentVotes + voteChange });
    });
}

export async function handleSave(userId: string, postId: string, isSaved: boolean) {
    const saveRef = db.collection(SAVES_COLLECTION).doc(`${userId}_${postId}`);

    if (isSaved) {
        await saveRef.set({ 
            userId, 
            postId, 
            createdAt: admin.firestore.FieldValue.serverTimestamp() 
        });
    } else {
        await saveRef.delete();
    }
}

export async function handleFollow(followerId: string, followingId: string, isFollowing: boolean) {
    const followRef = db.collection(FOLLOWS_COLLECTION).doc(`${followerId}_${followingId}`);
    const followerUserRef = db.collection(USERS_COLLECTION).doc(followerId);
    const followingUserRef = db.collection(USERS_COLLECTION).doc(followingId);

    if (isFollowing) {
        await db.runTransaction(async (transaction) => {
            transaction.set(followRef, { followerId, followingId, createdAt: admin.firestore.FieldValue.serverTimestamp() });
            transaction.update(followerUserRef, { followingCount: admin.firestore.FieldValue.increment(1) });
            transaction.update(followingUserRef, { followerCount: admin.firestore.FieldValue.increment(1) });
        });
    } else {
        await db.runTransaction(async (transaction) => {
            transaction.delete(followRef);
            transaction.update(followerUserRef, { followingCount: admin.firestore.FieldValue.increment(-1) });
            transaction.update(followingUserRef, { followerCount: admin.firestore.FieldValue.increment(-1) });
        });
    }
}


export async function handleCommentVote(userId: string, postId: string, commentId: string, voteType: 'up' | 'down') {
    const postDoc = await db.collection(POSTS_COLLECTION).doc(postId).get();
    const targetPostId = postDoc.data()?.isRepost ? postDoc.data()?.originalPost.id : postId;
    
    const voteRef = db.collection(POSTS_COLLECTION).doc(targetPostId).collection(COMMENTS_COLLECTION).doc(commentId).collection(COMMENT_VOTES_COLLECTION).doc(userId);
    const commentRef = db.collection(POSTS_COLLECTION).doc(targetPostId).collection(COMMENTS_COLLECTION).doc(commentId);

    return db.runTransaction(async (transaction) => {
        const voteDoc = await transaction.get(voteRef);
        const commentDoc = await transaction.get(commentRef);
        if (!commentDoc.exists) throw new Error("Comment not found");

        const commentData = commentDoc.data()!;
        let currentVotes = commentData.votes || 0;
        let voteChange = 0;

        if (voteDoc.exists) {
            const existingVote = voteDoc.data()?.type;
            if (existingVote === voteType) {
                transaction.delete(voteRef);
                voteChange = voteType === 'up' ? -1 : 1;
            } else {
                transaction.update(voteRef, { type: voteType });
                voteChange = voteType === 'up' ? 2 : -2;
            }
        } else {
            transaction.set(voteRef, { userId, postId: targetPostId, commentId, type: voteType, createdAt: admin.firestore.FieldValue.serverTimestamp() });
            voteChange = voteType === 'up' ? 1 : -1;
        }
        transaction.update(commentRef, { votes: currentVotes + voteChange });
    });
}

export async function handleCommentSave(userId: string, postId: string, commentId: string, isSaved: boolean) {
    const postDoc = await db.collection(POSTS_COLLECTION).doc(postId).get();
    const targetPostId = postDoc.data()?.isRepost ? postDoc.data()?.originalPost.id : postId;

    const saveRef = db.collection(POSTS_COLLECTION).doc(targetPostId).collection(COMMENTS_COLLECTION).doc(commentId).collection(COMMENT_SAVES_COLLECTION).doc(userId);

    if (isSaved) {
        await saveRef.set({ userId, postId: targetPostId, commentId, createdAt: admin.firestore.FieldValue.serverTimestamp() });
    } else {
        await saveRef.delete();
    }
}

export async function createSpace(spaceData: Omit<Space, 'id' | 'createdAt'>): Promise<string> {
    const spaceRef = await db.collection(SPACES_COLLECTION).add({
      ...spaceData,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
    });
    return spaceRef.id;
}

export async function getPublicSpaces(): Promise<Space[]> {
    const snapshot = await db.collection(SPACES_COLLECTION)
        .where('visibility', '==', 'public')
        .orderBy('createdAt', 'desc')
        .get();
        
    if (snapshot.empty) {
        return [];
    }

    return snapshot.docs.map(doc => {
        const data = doc.data();
        return {
            id: doc.id,
            ...data,
            createdAt: ((data.createdAt as admin.firestore.Timestamp).toDate()).toISOString(),
        } as Space;
    });
}

export async function getSpace(spaceId: string): Promise<Space | null> {
    const doc = await db.collection(SPACES_COLLECTION).doc(spaceId).get();
    if (!doc.exists) {
        return null;
    }
    const data = doc.data()!;
    return {
        id: doc.id,
        ...data,
        createdAt: ((data.createdAt as admin.firestore.Timestamp).toDate()).toISOString(),
    } as Space;
}

export async function getSavedPostsForUser(userId: string): Promise<Post[]> {
    const savesSnapshot = await db.collection(SAVES_COLLECTION).where('userId', '==', userId).get();
    
    if (savesSnapshot.empty) {
        return [];
    }

    const postIds = savesSnapshot.docs.map(doc => doc.data().postId);
    
    if (postIds.length === 0) {
        return [];
    }
    
    // Firestore 'in' queries are limited to 30 items.
    // We need to fetch posts in chunks if there are more than 30 saved posts.
    const postChunks: Post[] = [];
    for (let i = 0; i < postIds.length; i += 30) {
        const chunk = postIds.slice(i, i + 30);
        const postsSnapshot = await db.collection(POSTS_COLLECTION).where(admin.firestore.FieldPath.documentId(), 'in', chunk).get();
        const posts = postsSnapshot.docs.map(doc => {
            const data = doc.data();
            return {
                id: doc.id,
                ...data,
                createdAt: ((data.createdAt as admin.firestore.Timestamp)?.toDate() || new Date()).toISOString(),
            } as Post;
        });
        postChunks.push(...posts);
    }
    
    const enrichedPosts = await enrichPostsWithUserData(postChunks, userId);

    // Sort the final enriched posts by the original save date
    const sortedPosts = enrichedPosts.sort((a, b) => {
        const aIndex = postIds.indexOf(a.id);
        const bIndex = postIds.indexOf(b.id);
        return aIndex - bIndex;
    });

    return sortedPosts;
}
