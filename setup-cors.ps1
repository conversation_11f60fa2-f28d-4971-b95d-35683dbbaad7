# PowerShell script to configure Firebase Storage CORS
# Run this script as Administrator

Write-Host "Setting up Firebase Storage CORS configuration..." -ForegroundColor Green

# Check if Google Cloud SDK is installed
$gcloudPath = Get-Command gcloud -ErrorAction SilentlyContinue
if (-not $gcloudPath) {
    Write-Host "Google Cloud SDK not found. Please install it first:" -ForegroundColor Red
    Write-Host "https://cloud.google.com/sdk/docs/install" -ForegroundColor Yellow
    exit 1
}

# Authenticate with Google Cloud
Write-Host "Authenticating with Google Cloud..." -ForegroundColor Yellow
gcloud auth login

# Set the project
Write-Host "Setting project to politica-overview..." -ForegroundColor Yellow
gcloud config set project politica-overview

# Apply CORS configuration
Write-Host "Applying CORS configuration to Firebase Storage..." -ForegroundColor Yellow
gsutil cors set cors.json gs://politica-overview.appspot.com

# Verify CORS configuration
Write-Host "Verifying CORS configuration..." -ForegroundColor Yellow
gsutil cors get gs://politica-overview.appspot.com

Write-Host "CORS configuration completed!" -ForegroundColor Green
Write-Host "Please restart your development server and try uploading again." -ForegroundColor Yellow
