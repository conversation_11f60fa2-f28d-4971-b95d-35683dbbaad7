
import { auth } from '@/lib/firebase-admin';
import { cookies } from 'next/headers';
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { token } = body;

    if (!token) {
        return NextResponse.json({ error: 'Token is required' }, { status: 400 });
    }

    // Set session expiration to 5 days.
    const expiresIn = 60 * 60 * 24 * 5 * 1000;
    
    // Create the session cookie. This will also verify the ID token in the process.
    const sessionCookie = await auth.createSessionCookie(token, { expiresIn });

    // Set cookie policy for the session cookie.
    const options = {
      name: 'session',
      value: sessionCookie,
      maxAge: expiresIn,
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      path: '/',
      sameSite: 'lax' as const,
    };

    // Set the cookie in the response.
    const cookieStore = await cookies();
    cookieStore.set(options);

    return NextResponse.json({ success: true }, { status: 200 });

  } catch (error) {
    console.error('Login API error:', error);
    return NextResponse.json({ error: 'Authentication failed.' }, { status: 401 });
  }
}
