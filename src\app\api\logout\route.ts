
import { auth } from '@/lib/firebase-admin';
import { cookies } from 'next/headers';
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const sessionCookie = cookieStore.get('session')?.value;

    if (sessionCookie) {
      // Clear the session cookie from the browser
      cookieStore.delete('session');

      // Revoke the session on the server side to invalidate it completely.
      const decodedClaims = await auth.verifySessionCookie(sessionCookie).catch(() => null);
      if (decodedClaims) {
          await auth.revokeRefreshTokens(decodedClaims.sub);
      }
    }

    return NextResponse.json({ success: true }, { status: 200 });
  } catch (error) {
    console.error('Logout API error:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
