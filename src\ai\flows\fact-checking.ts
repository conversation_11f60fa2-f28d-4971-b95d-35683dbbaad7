'use server';

/**
 * @fileOverview Fact-checking flow that validates claims against authoritative sources.
 *
 * - factCheck - A function that takes a statement and returns a fact-checked analysis.
 * - FactCheckInput - The input type for the factCheck function.
 * - FactCheckOutput - The return type for the factCheck function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const FactCheckInputSchema = z.object({
  statement: z.string().describe('The statement to be fact-checked.'),
});
export type FactCheckInput = z.infer<typeof FactCheckInputSchema>;

const FactCheckOutputSchema = z.object({
  isAccurate: z.boolean().describe('Whether the statement is accurate.'),
  explanation: z.string().describe('An explanation of the fact-checking analysis, including sources.'),
  sources: z.array(z.string()).describe('A list of URLs for the sources used in the fact-checking analysis.'),
});
export type FactCheckOutput = z.infer<typeof FactCheckOutputSchema>;

export async function factCheck(input: FactCheckInput): Promise<FactCheckOutput> {
  return factCheckFlow(input);
}

const factCheckPrompt = ai.definePrompt({
  name: 'factCheckPrompt',
  input: {schema: FactCheckInputSchema},
  output: {schema: FactCheckOutputSchema},
  prompt: `You are an expert fact-checker. Given the following statement, determine if it is accurate based on authoritative sources.

Statement: {{{statement}}}

Provide a detailed explanation of your analysis, including the sources you used. Return a JSON object with the following format:
{
  "isAccurate": true or false,
  "explanation": "Detailed explanation of the fact-checking analysis",
  "sources": ["url1", "url2", ...]
}
`,
});

const factCheckFlow = ai.defineFlow(
  {
    name: 'factCheckFlow',
    inputSchema: FactCheckInputSchema,
    outputSchema: FactCheckOutputSchema,
  },
  async input => {
    const {output} = await factCheckPrompt(input);
    return output!;
  }
);
