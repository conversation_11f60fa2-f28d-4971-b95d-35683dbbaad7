'use client';

import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

export default function StatCard({ icon: Icon, title, value, change, changeColor }: { icon: React.ElementType, title: string, value: string, change: string, changeColor: string }) {
  return (
    <Card className="border-2 border-black rounded-lg shadow-[4px_4px_0px_#000]">
      <CardContent className="p-4 flex items-center gap-4">
        <div className="p-3 bg-muted rounded-md border-2 border-black">
          <Icon className="h-6 w-6 text-muted-foreground" />
        </div>
        <div className="flex-1">
          <p className="text-2xl sm:text-3xl font-bold font-headline">{value}</p>
          <p className="text-sm font-medium text-muted-foreground leading-snug">{title}</p>
        </div>
        <Badge className={cn("border-2 border-black whitespace-nowrap shrink-0 self-start", changeColor)}>{change}</Badge>
      </CardContent>
    </Card>
  );
}
