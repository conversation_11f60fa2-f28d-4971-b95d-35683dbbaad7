
export interface CareerItem {
  role: string;
  company: string;
  period: string;
}

export interface SkillItem {
  name: string;
  endorsements: number;
}

export interface AchievementItem {
  name: string;
  icon: string;
}

export interface UserProfile {
  name: string;
  title: string;
  avatarUrl: string;
  reputation: number;
  verification: 'Professional' | 'Institutional' | 'Community';
  career: CareerItem[];
  skills: SkillItem[];
  achievements: AchievementItem[];
  followerCount: number;
  followingCount: number;
}
