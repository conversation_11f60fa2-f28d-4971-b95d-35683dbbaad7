
'use client';

import { useState, useEffect } from 'react';
import type { Comment as CommentType } from "@/types/comment";
import type { UserProfile } from '@/types/user';
import { Avatar, AvatarFallback, AvatarImage } from "./ui/avatar";
import { formatDistanceToNow } from "date-fns";
import { Button } from "./ui/button";
import { MoreHorizontal, Minus, Plus } from "lucide-react";
import Link from "next/link";
import CommentActions from "./comment-actions";
import AddCommentForm from './add-comment-form';
import { cn } from '@/lib/utils';
import { Card, CardContent } from './ui/card';

export default function Comment({ 
  comment, 
  currentUserId, 
  currentUserProfile 
}: { 
  comment: CommentType, 
  currentUserId: string | null,
  currentUserProfile: UserProfile | null 
}) {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isReplying, setIsReplying] = useState(false);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  const hasReplies = comment.replies && comment.replies.length > 0;

  const handleReplyClick = () => {
    if (!currentUserProfile) {
        // In a real app, you might want to trigger a login modal here
        // For now, we just prevent the form from opening.
        return;
    }
    setIsReplying(!isReplying);
  }

  return (
    <div className="flex flex-col gap-4">
        <Card className="border-2 border-black rounded-lg shadow-[4px_4px_0px_black]">
            <CardContent className="p-4">
                <div className="flex gap-4">
                     <Link href={`/post/${comment.postId}/comment/${comment.id}`} className="z-10 shrink-0">
                        <Avatar className="h-10 w-10 border-2 border-black">
                            <AvatarImage src={comment.authorAvatar} alt={`@${comment.authorName}`} />
                            <AvatarFallback>{comment.authorName.substring(0, 2).toUpperCase()}</AvatarFallback>
                        </Avatar>
                    </Link>
                    <div className="flex-1">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2 text-sm flex-wrap">
                                <span className="font-bold text-foreground hover:underline cursor-pointer">{comment.authorName}</span>
                                <span className="text-muted-foreground">@{comment.authorName.toLowerCase().replace(/ /g, '')}</span>
                                <span className="text-muted-foreground">·</span>
                                <Link href={`/post/${comment.postId}/comment/${comment.id}`}>
                                    <span className="text-muted-foreground hover:underline cursor-pointer">
                                        {isMounted ? formatDistanceToNow(new Date(comment.createdAt), { addSuffix: true }) : null}
                                    </span>
                                </Link>
                            </div>
                            <div className="flex items-center">
                                {hasReplies && (
                                    <Button variant="ghost" size="icon" className="h-8 w-8 text-muted-foreground" onClick={() => setIsCollapsed(!isCollapsed)} aria-label={isCollapsed ? 'Expand thread' : 'Collapse thread'}>
                                        {isCollapsed ? <Plus className="h-4 w-4" /> : <Minus className="h-4 w-4" />}
                                    </Button>
                                )}
                                <Button variant="ghost" size="icon" className="h-8 w-8 text-muted-foreground">
                                    <MoreHorizontal className="h-4 w-4" />
                                </Button>
                            </div>
                        </div>
                        
                        <p className="mt-2 text-base leading-relaxed">{comment.content}</p>
                        
                        <CommentActions 
                        comment={comment} 
                        currentUserId={currentUserId}
                        onReplyClick={handleReplyClick} 
                        />
                    </div>
                </div>
            </CardContent>
        </Card>

        {isReplying && currentUserProfile && (
            <div className="pl-8 flex gap-4 pt-2">
                <Avatar className="h-10 w-10 border-2 border-black">
                    <AvatarImage src={currentUserProfile.avatarUrl} alt={currentUserProfile.name}/>
                    <AvatarFallback>{currentUserProfile.name.substring(0, 2).toUpperCase()}</AvatarFallback>
                </Avatar>
                <div className="flex-1">
                    <AddCommentForm 
                        postId={comment.postId} 
                        parentCommentId={comment.id}
                        onCommentAdded={() => setIsReplying(false)}
                    />
                </div>
            </div>
        )}

        {hasReplies && (
          <div className={cn("pl-8 space-y-8 border-l-2 border-muted/80 ml-5", isCollapsed && "hidden")}>
            {comment.replies.map(reply => (
              <Comment 
                key={reply.id} 
                comment={reply} 
                currentUserId={currentUserId} 
                currentUserProfile={currentUserProfile}
              />
            ))}
          </div>
        )}
      </div>
  );
}
