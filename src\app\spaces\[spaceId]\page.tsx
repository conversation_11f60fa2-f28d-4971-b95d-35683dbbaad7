
import { getSpace } from "@/lib/firestore";
import { notFound } from "next/navigation";
import PageLayout from "@/components/page-layout";
import AppSidebar from "@/components/app-sidebar";
import { But<PERSON> } from "@/components/ui/button";
import { 
    Menu, 
    ArrowLeft,
    LayoutGrid,
    Users,
    Globe,
    Lock,
    Eye,
    Tag,
    BookOpen
} from "lucide-react";
import { Sheet, Sheet<PERSON>ontent, SheetTrigger, SheetTitle } from "@/components/ui/sheet";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Card, CardHeader, CardTitle, CardContent, CardDescription } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";
import { Separator } from "@/components/ui/separator";

function AppHeader({ spaceName }: { spaceName: string }) {
  return (
    <header className="flex h-16 items-center gap-4 border-b-2 border-black bg-background px-6 sticky top-0 z-10">
        <Sheet>
            <SheetTrigger asChild>
              <Button
                variant="outline"
                size="icon"
                className="shrink-0 md:hidden border-2 border-black"
              >
                <Menu className="h-5 w-5" />
                <span className="sr-only">Toggle navigation menu</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="p-0 border-r-2 border-black bg-primary">
                <SheetTitle className="sr-only">Navigation Menu</SheetTitle>
                <AppSidebar />
            </SheetContent>
        </Sheet>
        <Button variant="ghost" size="icon" asChild>
          <Link href="/spaces/discover" aria-label="Back to Discover Spaces">
            <ArrowLeft />
          </Link>
        </Button>
        <div className="text-xl font-bold font-headline flex items-center gap-2">
            <LayoutGrid className="h-5 w-5"/>
            <span>{spaceName}</span>
        </div>
    </header>
  );
}

function VisibilityInfo({ visibility }: { visibility: 'public' | 'private' | 'secret'}) {
    const Icon = visibility === 'public' ? Globe : Lock;
    const text = visibility.charAt(0).toUpperCase() + visibility.slice(1);
    return (
        <div className="flex items-center gap-2">
            <Icon className="h-5 w-5" />
            <span className="font-semibold">{text}</span>
        </div>
    );
}


export default async function SpacePage({ params }: { params: { spaceId: string } }) {
    const space = await getSpace(params.spaceId);

    if (!space) {
        notFound();
    }

    return (
        <PageLayout>
            <AppHeader spaceName={space.name} />
            <main className="flex-1 bg-background">
                <div className="p-4 md:p-6 lg:p-8">
                    <div className="flex flex-col md:flex-row items-start gap-6 mb-8">
                        <Avatar className="h-24 w-24 rounded-lg border-4 border-black">
                            <AvatarImage src={space.creatorAvatar} />
                            <AvatarFallback>{space.name.substring(0,2)}</AvatarFallback>
                        </Avatar>
                        <div className="flex-1">
                            <h1 className="text-3xl md:text-4xl font-bold font-headline">{space.name}</h1>
                            <p className="text-lg text-muted-foreground mt-2">{space.description}</p>
                            <div className="flex flex-wrap items-center gap-4 mt-4 text-sm text-muted-foreground">
                                <div className="flex items-center gap-2">
                                    <Avatar className="h-6 w-6 border-2 border-black">
                                        <AvatarImage src={space.creatorAvatar} />
                                        <AvatarFallback>{space.creatorName.charAt(0)}</AvatarFallback>
                                    </Avatar>
                                    <span>Created by <span className="font-semibold text-foreground">{space.creatorName}</span></span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Users className="h-4 w-4" />
                                    <span>{space.memberCount} Members</span>
                                </div>
                            </div>
                        </div>
                        <Button className="bg-accent text-accent-foreground border-2 border-black shadow-[2px_2px_0px_#000] whitespace-nowrap">
                            Join Space
                        </Button>
                    </div>

                    <div className="grid gap-6 grid-cols-1 lg:grid-cols-3">
                        <div className="lg:col-span-2 space-y-6">
                            <Card className="border-2 border-black shadow-[4px_4px_0px_black] rounded-lg">
                                <CardHeader>
                                    <CardTitle className="font-bold">Space Feed</CardTitle>
                                    <CardDescription>Recent activity and discussions.</CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="text-center text-muted-foreground py-12">
                                        <p>No posts in this space yet. Be the first!</p>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>

                        <div className="lg:col-span-1 space-y-6">
                            <Card className="border-2 border-black shadow-[4px_4px_0px_black] rounded-lg">
                                <CardHeader>
                                    <CardTitle className="font-bold">About this Space</CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="flex items-start gap-3">
                                        <BookOpen className="h-5 w-5 text-muted-foreground mt-1 flex-shrink-0" />
                                        <div>
                                            <h4 className="font-semibold">Category</h4>
                                            <p className="text-muted-foreground">{space.category}</p>
                                        </div>
                                    </div>
                                    <Separator />
                                    <div className="flex items-start gap-3">
                                        <Eye className="h-5 w-5 text-muted-foreground mt-1 flex-shrink-0" />
                                        <div>
                                            <h4 className="font-semibold">Visibility</h4>
                                            <VisibilityInfo visibility={space.visibility} />
                                        </div>
                                    </div>
                                    <Separator />
                                    <div className="flex items-start gap-3">
                                        <Tag className="h-5 w-5 text-muted-foreground mt-1 flex-shrink-0" />
                                        <div>
                                            <h4 className="font-semibold">Tags</h4>
                                            <div className="flex flex-wrap gap-2 mt-1">
                                                {space.tags.map(tag => (
                                                    <Badge key={tag} variant="secondary" className="border-black border">{tag}</Badge>
                                                ))}
                                            </div>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </div>
            </main>
        </PageLayout>
    );
}
