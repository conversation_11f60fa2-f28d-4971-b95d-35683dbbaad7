
import { getPost, getComments, getUserProfile } from "@/lib/firestore";
import { getSession } from "@/lib/session";
import PageLayout from "@/components/page-layout";
import { Button } from "@/components/ui/button";
import AppSidebar from "@/components/app-sidebar";
import { 
    <PERSON>u, 
    ArrowLeft,
} from "lucide-react";
import { Sheet, Sheet<PERSON>ontent, SheetTrigger, SheetTitle } from "@/components/ui/sheet";
import { notFound } from "next/navigation";
import Link from "next/link";
import PostDetail from "@/components/post-detail";
import type { UserProfile } from "@/types/user";


export default async function PostPage({ params }: { params: Promise<{ postId: string }> }) {
  const session = await getSession();
  const { postId } = await params;
  const post = await getPost(postId, session?.uid);

  if (!post) {
    notFound();
  }

  // If it's a repost, get comments for the original post.
  const commentsPostId = post.isRepost && post.originalPost ? post.originalPost.id : post.id;
  const [flatComments, currentUserProfile] = await Promise.all([
    getComments(commentsPostId, session?.uid),
    session ? getUserProfile(session.uid) : Promise.resolve(null as UserProfile | null)
  ]);
  
  return (
    <PageLayout>
      <header className="flex h-16 shrink-0 items-center gap-4 border-b-2 border-black bg-background px-4 md:px-6 sticky top-0 z-10">
        <Sheet>
            <SheetTrigger asChild>
              <Button
                variant="outline"
                size="icon"
                className="shrink-0 md:hidden border-2 border-black"
              >
                <Menu className="h-5 w-5" />
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="p-0 border-r-2 border-black bg-primary">
                <SheetTitle className="sr-only">Navigation Menu</SheetTitle>
                <AppSidebar />
            </SheetContent>
        </Sheet>
        <Button variant="ghost" size="icon" asChild>
          <Link href="/dashboard" aria-label="Back to dashboard">
            <ArrowLeft />
          </Link>
        </Button>
        <h1 className="text-xl font-bold font-headline">Post</h1>
      </header>
      <PostDetail
        post={post}
        flatComments={flatComments}
        currentUserProfile={currentUserProfile}
        currentUserId={session?.uid || null}
      />
    </PageLayout>
  );
}
