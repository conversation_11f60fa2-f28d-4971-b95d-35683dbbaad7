'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, Loader2 } from 'lucide-react';

export default function CorsTest() {
  const [testing, setTesting] = useState(false);
  const [result, setResult] = useState<'success' | 'error' | null>(null);
  const [message, setMessage] = useState('');

  const testCors = async () => {
    setTesting(true);
    setResult(null);
    setMessage('');

    try {
      // Test CORS by making a simple request to Firebase Storage
      const testUrl = 'https://firebasestorage.googleapis.com/v0/b/politica-overview.firebasestorage.app/o';

      const response = await fetch(testUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        setResult('success');
        setMessage('CORS is configured correctly! Firebase Storage is accessible.');
      } else {
        setResult('error');
        setMessage(`CORS test failed with status: ${response.status}`);
      }
    } catch (error) {
      setResult('error');
      if (error instanceof Error) {
        if (error.message.includes('CORS')) {
          setMessage('CORS policy is blocking requests. Please configure CORS for Firebase Storage.');
        } else {
          setMessage(`Network error: ${error.message}`);
        }
      } else {
        setMessage('Unknown error occurred during CORS test.');
      }
    } finally {
      setTesting(false);
    }
  };

  return (
    <Card className="border-2 border-black">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          🔧 CORS Configuration Test
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <p className="text-sm text-gray-600">
          Test if Firebase Storage CORS is properly configured for localhost development.
        </p>
        
        <Button 
          onClick={testCors} 
          disabled={testing}
          className="w-full"
        >
          {testing ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Testing CORS...
            </>
          ) : (
            'Test CORS Configuration'
          )}
        </Button>

        {result && (
          <div className="flex items-start gap-3 p-3 rounded-lg border">
            {result === 'success' ? (
              <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
            ) : (
              <XCircle className="h-5 w-5 text-red-500 mt-0.5" />
            )}
            <div className="flex-1">
              <Badge 
                variant={result === 'success' ? 'default' : 'destructive'}
                className="mb-2"
              >
                {result === 'success' ? 'Success' : 'Error'}
              </Badge>
              <p className="text-sm">{message}</p>
            </div>
          </div>
        )}

        {result === 'error' && (
          <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <h4 className="font-medium text-yellow-800 mb-2">How to Fix CORS Issues:</h4>
            <ol className="text-sm text-yellow-700 space-y-1 list-decimal list-inside">
              <li>Go to <a href="https://console.cloud.google.com/storage/browser?project=politica-overview" target="_blank" rel="noopener noreferrer" className="underline">Google Cloud Console</a></li>
              <li>Find bucket: <code>politica-overview.firebasestorage.app</code></li>
              <li>Click on bucket name → Permissions tab</li>
              <li>Add principal: <code>allUsers</code> with role: <code>Storage Object Viewer</code></li>
              <li>Or run: <code>gsutil cors set cors.json gs://politica-overview.firebasestorage.app</code></li>
            </ol>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
