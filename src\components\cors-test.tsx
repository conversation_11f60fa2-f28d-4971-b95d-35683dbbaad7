'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Check<PERSON>ircle, XCircle, Loader2 } from 'lucide-react';

export default function CorsTest() {
  const [testing, setTesting] = useState(false);
  const [result, setResult] = useState<'success' | 'error' | null>(null);
  const [message, setMessage] = useState('');

  const testCors = async () => {
    setTesting(true);
    setResult(null);
    setMessage('');

    try {
      // Test CORS by trying to access Firebase Storage with proper authentication
      const { validateStorageConfig } = await import('@/lib/firebase-storage');

      const isValid = validateStorageConfig();

      if (isValid) {
        // Try to create a test blob and upload it to test CORS
        const testBlob = new Blob(['test'], { type: 'text/plain' });
        const testFile = new File([testBlob], 'cors-test.txt', { type: 'text/plain' });

        // Import Firebase functions
        const { getStorage, ref, uploadBytes, deleteObject } = await import('firebase/storage');
        const { firebaseApp } = await import('@/lib/firebase');

        const storage = getStorage(firebaseApp);
        const testRef = ref(storage, `test/cors-test-${Date.now()}.txt`);

        // Try to upload a small test file
        await uploadBytes(testRef, testFile);

        // Clean up the test file
        await deleteObject(testRef);

        setResult('success');
        setMessage('CORS is configured correctly! Firebase Storage is accessible and uploads work.');
      } else {
        setResult('error');
        setMessage('Firebase Storage configuration is invalid.');
      }
    } catch (error) {
      setResult('error');
      if (error instanceof Error) {
        if (error.message.includes('CORS') || error.message.includes('cross-origin')) {
          setMessage('CORS policy is blocking requests. Please configure CORS for Firebase Storage.');
        } else if (error.message.includes('permission') || error.message.includes('403')) {
          setMessage('Permission denied. Check Firebase Storage rules and authentication.');
        } else {
          setMessage(`Error: ${error.message}`);
        }
      } else {
        setMessage('Unknown error occurred during CORS test.');
      }
    } finally {
      setTesting(false);
    }
  };

  return (
    <Card className="border-2 border-black">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          🔧 CORS Configuration Test
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <p className="text-sm text-gray-600">
          Test if Firebase Storage CORS is properly configured for localhost development.
        </p>
        
        <Button 
          onClick={testCors} 
          disabled={testing}
          className="w-full"
        >
          {testing ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Testing CORS...
            </>
          ) : (
            'Test CORS Configuration'
          )}
        </Button>

        {result && (
          <div className="flex items-start gap-3 p-3 rounded-lg border">
            {result === 'success' ? (
              <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
            ) : (
              <XCircle className="h-5 w-5 text-red-500 mt-0.5" />
            )}
            <div className="flex-1">
              <Badge 
                variant={result === 'success' ? 'default' : 'destructive'}
                className="mb-2"
              >
                {result === 'success' ? 'Success' : 'Error'}
              </Badge>
              <p className="text-sm">{message}</p>
            </div>
          </div>
        )}

        {result === 'error' && (
          <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <h4 className="font-medium text-yellow-800 mb-2">How to Fix CORS Issues:</h4>
            <ol className="text-sm text-yellow-700 space-y-1 list-decimal list-inside">
              <li>Install Google Cloud SDK: <a href="https://cloud.google.com/sdk/docs/install" target="_blank" rel="noopener noreferrer" className="underline">Download here</a></li>
              <li>Authenticate: <code>gcloud auth login</code></li>
              <li>Set CORS: <code>gsutil cors set cors.json gs://politica-overview.firebasestorage.app</code></li>
              <li>Or use <a href="https://console.cloud.google.com/storage/browser/politica-overview.firebasestorage.app?project=politica-overview" target="_blank" rel="noopener noreferrer" className="underline">Google Cloud Console</a></li>
              <li>In bucket settings, add CORS configuration from <code>cors.json</code></li>
            </ol>
          </div>
        )}

        <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <h4 className="font-medium text-blue-800 mb-2">📋 CORS Configuration Status:</h4>
          <p className="text-sm text-blue-700 mb-2">
            The application is configured to work with Firebase Storage. If uploads fail, CORS may need manual setup.
          </p>
          <div className="text-xs text-blue-600 space-y-1">
            <div>✅ Storage Rules: Deployed</div>
            <div>✅ Firebase Config: Valid</div>
            <div>⚠️ CORS: May require manual setup for localhost</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
