
'use client';
import { Ava<PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { 
    MessageCircle, 
    Repeat2, 
    Bookmark, 
    Share2, 
    MoreHorizontal,
    ArrowUp,
    ArrowDown,
    Loader2,
    MessageSquareQuote,
} from "lucide-react";
import type { Post } from '@/types/post';
import { formatDistanceToNow } from 'date-fns';
import { useState, useTransition, useEffect } from "react";
import { voteAction, saveAction, followAction, repostAction, repostCommentAction } from "@/app/actions";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "./ui/dropdown-menu";
import PostMedia from "./post-media";
import { useToast } from "@/hooks/use-toast";
import { cn } from "@/lib/utils";
import Link from 'next/link';
import type { Comment } from "@/types/comment";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Textarea } from "./ui/textarea";

const PostContent = ({ post, isDetailPage, isMounted }: { post: Post, isDetailPage?: boolean, isMounted: boolean }) => {
  const isPostRepost = post.isRepost && post.originalPost;
  const isCommentRepost = post.isRepost && post.originalComment;

  let mainContent: React.ReactNode;
  let quotedContent: React.ReactNode | null = null;
  let linkHref: string;
  let contextContent: React.ReactNode | null = null;
  
  const displayData = isPostRepost ? post.originalPost! : (isCommentRepost ? post.originalComment! : post);
  const repostAuthor = post.isRepost ? post : null;

  if (isCommentRepost) {
      linkHref = `/post/${'postId' in displayData ? displayData.postId : displayData.id}/comment/${displayData.id}`;
      quotedContent = (
        <div className="mt-2 border-2 border-black rounded-lg p-3">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Avatar className="h-5 w-5 border-2 border-black">
                    <AvatarImage src={displayData.authorAvatar} alt={`@${displayData.authorName}`} data-ai-hint="user avatar" />
                    <AvatarFallback>{displayData.authorName.substring(0, 1)}</AvatarFallback>
                </Avatar>
                <span className="font-bold text-foreground">{displayData.authorName}</span>
                <span>· {isMounted ? formatDistanceToNow(new Date(displayData.createdAt), { addSuffix: true }) : null}</span>
            </div>
            <p className="text-sm mt-2">{displayData.content}</p>
        </div>
      );
  } else if (isPostRepost) {
    linkHref = `/post/${displayData.id}`;
    quotedContent = (
        <div className="mt-2 border-2 border-black rounded-lg p-3">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Avatar className="h-5 w-5 border-2 border-black">
                    <AvatarImage src={displayData.authorAvatar} alt={`@${displayData.authorName}`} data-ai-hint="user avatar" />
                    <AvatarFallback>{displayData.authorName.substring(0, 1)}</AvatarFallback>
                </Avatar>
                <span className="font-bold text-foreground">{displayData.authorName}</span>
                <span>· {isMounted ? formatDistanceToNow(new Date(displayData.createdAt), { addSuffix: true }) : null}</span>
            </div>
            <p className="text-sm mt-2">{displayData.content}</p>
        </div>
      );
  } else {
     linkHref = `/post/${displayData.id}`;
  }


  mainContent = (
      <div className="space-y-2">
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          <div className="flex items-center gap-2 flex-wrap">
            <Avatar className="h-6 w-6 border-2 border-black">
              <AvatarImage src={(repostAuthor || displayData).authorAvatar} alt={`@${(repostAuthor || displayData).authorName}`} data-ai-hint="user avatar" />
              <AvatarFallback>{(repostAuthor || displayData).authorName.substring(0,2).toUpperCase()}</AvatarFallback>
            </Avatar>
            <span className="font-bold text-foreground">{(repostAuthor || displayData).authorName}</span>
            <Badge variant="secondary" className="border border-black text-xs">
              {(() => {
                const data = repostAuthor || displayData;
                return 'space' in data ? data.space : 'General';
              })()}
            </Badge>
            <span>· {isMounted ? formatDistanceToNow(new Date((repostAuthor || displayData).createdAt), { addSuffix: true }) : null}</span>
          </div>
          <Button variant="ghost" size="icon" className="h-8 w-8"><MoreHorizontal className="h-4 w-4"/></Button>
        </div>

        {post.content && <p className="text-base leading-relaxed">{post.content}</p>}

        {/* Display media if available */}
        {'media' in displayData && displayData.media && displayData.media.length > 0 && (
          <PostMedia media={displayData.media} />
        )}

        {quotedContent}
      </div>
    );
  

  const relevanceReasoningContent = post.relevanceReasoning ? (
    <p className="mt-2 text-xs text-muted-foreground bg-muted p-2 rounded-md border border-black">
      <strong>For you:</strong> {post.relevanceReasoning}
    </p>
  ) : null;

  const renderMainContent = () => (
    <>
        {mainContent}
        {relevanceReasoningContent}
    </>
  );

  if (isDetailPage) {
    return (
      <div>
        {renderMainContent()}
        {contextContent}
      </div>
    )
  }

  return (
    <div>
      <Link href={linkHref} className="block">
        {renderMainContent()}
      </Link>
      {contextContent}
    </div>
  );
};


export default function FeedPost({ post: initialPost, currentUserId, isDetailPage = false, onAction }: { post: Post, currentUserId: string | null, isDetailPage?: boolean, onAction?: () => void }) {
  const [post, setPost] = useState(initialPost);
  const [isMounted, setIsMounted] = useState(false);
  const [isFollowed, setIsFollowed] = useState(post.isFollowed || false);
  const [isQuoteDialogOpen, setQuoteDialogOpen] = useState(false);
  const [quoteContent, setQuoteContent] = useState('');
  
  const [isVotePending, startVoteTransition] = useTransition();
  const [isSavePending, startSaveTransition] = useTransition();
  const [isFollowPending, startFollowTransition] = useTransition();
  const [isRepostPending, startRepostTransition] = useTransition();
  const { toast } = useToast();

  useEffect(() => {
    setIsMounted(true);
  }, []);
  
  useEffect(() => {
    setPost(initialPost);
  }, [initialPost]);


  const isPostRepost = post.isRepost && post.originalPost;
  const isCommentRepost = post.isRepost && post.originalComment;

  // The data for the content being displayed (either the original post, original comment, or the post itself)
  const displayData = isPostRepost 
      ? post.originalPost! 
      : isCommentRepost 
      ? post.originalComment! 
      : post;
  
  // The post we are actually interacting with (the repost wrapper or the post itself)
  const interactionPost = post;

  const handleVote = (voteType: 'up' | 'down') => {
    if (!currentUserId) {
        toast({ variant: "destructive", title: "Please log in to vote." });
        return;
    }
    startVoteTransition(async () => {
      const result = await voteAction(interactionPost.id, voteType);
      if (result.success) {
        onAction?.();
      } else {
          toast({ variant: "destructive", title: "Vote failed", description: result.message });
      }
    });
  };

  const handleSave = () => {
    if (!currentUserId) {
        toast({ variant: "destructive", title: "Please log in to save posts." });
        return;
    }
    startSaveTransition(async () => {
      const result = await saveAction(interactionPost.id, !interactionPost.isSaved);
      if (result.success) {
          onAction?.();
      } else {
          toast({ variant: "destructive", title: "Action failed", description: result.message });
      }
    });
  };

  const handleFollow = (e: React.MouseEvent) => {
      e.stopPropagation();
      if (!currentUserId) {
        toast({ variant: "destructive", title: "Please log in to follow users." });
        return;
      }
      if (currentUserId === displayData.authorId) return;

      startFollowTransition(async () => {
        const originalFollowState = isFollowed;
        setIsFollowed(!originalFollowState);
        const result = await followAction(displayData.authorId, !originalFollowState);
        if (result.success) {
            onAction?.();
        } else {
            setIsFollowed(originalFollowState);
            toast({ variant: "destructive", title: "Action failed", description: result.message });
        }
      });
  };
  
  const handleShare = (e: React.MouseEvent) => {
      e.stopPropagation();
      let postUrl: string;
      if (isCommentRepost) {
        postUrl = `${window.location.origin}/post/${(displayData as Comment).postId}/comment/${displayData.id}`;
      } else {
        postUrl = `${window.location.origin}/post/${displayData.id}`;
      }
      navigator.clipboard.writeText(postUrl);
      toast({ title: "Link Copied!", description: "Post URL has been copied to your clipboard." });
  };
  
  const handlePostRepost = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (!currentUserId) {
      toast({ variant: "destructive", title: "Please log in to repost." });
      return;
    }
    startRepostTransition(async () => {
      const result = await repostAction((displayData as Post).id);
      if (result.success) {
        toast({ title: "Post Reposted!", description: "You have successfully reposted." });
        onAction?.();
      } else {
        toast({ variant: "destructive", title: "Repost Failed", description: result.message });
      }
    });
  }

  const handleCommentRepost = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (!currentUserId) {
      toast({ variant: "destructive", title: "Please log in to repost." });
      return;
    }
    startRepostTransition(async () => {
      const comment = displayData as Comment;
      const result = await repostCommentAction(comment.postId, comment.id);
      if (result.success) {
        toast({ title: "Comment Reposted!", description: "The comment has been reposted to the main feed." });
        onAction?.();
      } else {
        toast({ variant: "destructive", title: "Repost Failed", description: result.message });
      }
    });
  }
  
  const handleQuoteSubmit = () => {
      if (!currentUserId) {
        toast({ variant: "destructive", title: "Please log in to repost." });
        return;
      }
      startRepostTransition(async () => {
        const result = isCommentRepost
          ? await repostCommentAction((displayData as Comment).postId, displayData.id, quoteContent)
          : await repostAction((displayData as Post).id, quoteContent);

        if (result.success) {
          toast({ title: "Post Quoted!", description: "You have successfully quoted the post." });
          setQuoteDialogOpen(false);
          setQuoteContent('');
          onAction?.();
        } else {
          toast({ variant: "destructive", title: "Quote Failed", description: result.message });
        }
      });
  }

  const commentsLink = isCommentRepost 
    ? `/post/${(displayData as Comment).postId}/comment/${displayData.id}`
    : `/post/${displayData.id}`;

  const QuotedPostPreview = () => (
    <div className="mt-4 border-2 border-black rounded-lg p-3 space-y-2">
      <div className="flex items-center gap-2 text-sm text-muted-foreground">
        <Avatar className="h-5 w-5 border-2 border-black">
          <AvatarImage src={displayData.authorAvatar} alt={`@${displayData.authorName}`} />
          <AvatarFallback>{displayData.authorName.substring(0, 1)}</AvatarFallback>
        </Avatar>
        <span className="font-bold text-foreground">{displayData.authorName}</span>
        <span>· {isMounted ? formatDistanceToNow(new Date(displayData.createdAt), { addSuffix: true }) : null}</span>
      </div>
      <p className="text-sm line-clamp-4">{displayData.content}</p>
    </div>
  );

  return (
    <>
      <Card className="border-2 border-black rounded-lg">
        <CardContent className="p-4">
          {post.isRepost && !post.content && (
            <div className="text-sm text-muted-foreground flex items-center gap-2 mb-2 px-10">
              <Repeat2 className="h-4 w-4" />
              <span>{interactionPost.authorName} reposted</span>
            </div>
          )}
          <div className="flex gap-4">
            <div className="flex flex-col items-center gap-1 text-muted-foreground pt-10">
              <Button variant="ghost" size="icon" className={cn("h-8 w-8", interactionPost.userVote === 'up' && 'text-accent')} onClick={() => handleVote('up')} disabled={isVotePending}>
                <ArrowUp className="h-5 w-5" />
              </Button>
              <span className={cn("font-bold text-sm", isVotePending ? "text-muted-foreground" : "text-foreground")}>
                  {isVotePending ? <Loader2 className="h-4 w-4 animate-spin"/> : interactionPost.votes}
              </span>
              <Button variant="ghost" size="icon" className={cn("h-8 w-8", interactionPost.userVote === 'down' && 'text-blue-600')} onClick={() => handleVote('down')} disabled={isVotePending}>
                <ArrowDown className="h-5 w-5" />
              </Button>
            </div>
            <div className="flex-1">
              <PostContent post={post} isDetailPage={isDetailPage} isMounted={isMounted} />

              <div className="mt-4 flex items-center justify-around gap-2 text-sm text-muted-foreground border-2 border-black rounded-lg p-1">
                <Button asChild variant="ghost" className="flex items-center gap-1 px-2 hover:bg-muted w-full justify-center">
                  <Link href={commentsLink}>
                      <MessageCircle className="h-4 w-4" />
                      <span>{interactionPost.commentsCount}</span>
                  </Link>
                </Button>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="flex items-center gap-1 px-2 hover:bg-muted w-full justify-center" disabled={isRepostPending || (post.isRepost && !post.content) }>
                      {isRepostPending ? <Loader2 className="h-4 w-4 animate-spin"/> : <Repeat2 className="h-4 w-4" />}
                      <span>Repost</span>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="border-2 border-black">
                      <DropdownMenuItem 
                        onSelect={isCommentRepost ? handleCommentRepost : handlePostRepost} 
                        disabled={post.isRepost || isRepostPending}
                      >
                          <Repeat2 className="mr-2 h-4 w-4" /> Repost
                      </DropdownMenuItem>
                       <DropdownMenuItem 
                        onSelect={() => setQuoteDialogOpen(true)}
                        disabled={post.isRepost || isRepostPending}
                       >
                          <MessageSquareQuote className="mr-2 h-4 w-4" /> Quote
                      </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
                
                <Button variant="ghost" className="flex items-center gap-1 px-2 hover:bg-muted w-full justify-center" onClick={handleSave} disabled={isSavePending}>
                    <Bookmark className={cn("h-4 w-4", interactionPost.isSaved && 'fill-current text-accent')} />
                    <span>{isSavePending ? '...' : (interactionPost.isSaved ? 'Saved' : 'Save')}</span>
                </Button>
                <Button variant="ghost" className="flex items-center gap-1 px-2 hover:bg-muted w-full justify-center" onClick={handleShare}>
                  <Share2 className="h-4 w-4" />
                  <span>Share</span>
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Dialog open={isQuoteDialogOpen} onOpenChange={setQuoteDialogOpen}>
        <DialogContent className="sm:max-w-[625px] border-2 border-black shadow-[8px_8px_0px_black] rounded-lg">
          <DialogHeader>
            <DialogTitle className="font-headline text-2xl">Quote Post</DialogTitle>
            <DialogDescription>Add your own thoughts to this post.</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <Textarea
              placeholder="Add a comment..."
              value={quoteContent}
              onChange={(e) => setQuoteContent(e.target.value)}
              className="min-h-[100px] border-2 border-black"
            />
            <QuotedPostPreview />
          </div>
          <div className="flex justify-end">
            <Button onClick={handleQuoteSubmit} disabled={isRepostPending || quoteContent.length === 0} className="bg-accent text-accent-foreground border-2 border-black shadow-[2px_2px_0px_#000]">
                {isRepostPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Post
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}
