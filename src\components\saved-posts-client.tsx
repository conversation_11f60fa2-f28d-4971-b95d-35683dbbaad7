
'use client';
import type { Post } from "@/types/post";
import FeedPost from "./feed-post";
import { Bookmark, AlertTriangle } from "lucide-react";

export default function SavedPostsClient({ posts, currentUserId }: { posts: Post[], currentUserId: string }) {
  if (posts.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center text-center text-muted-foreground p-8 rounded-lg border-2 border-dashed">
        <Bookmark className="h-12 w-12 mb-4" />
        <h3 className="text-xl font-bold font-headline">No Saved Posts</h3>
        <p className="mt-2">You haven't saved any posts yet. Click the bookmark icon on a post to save it for later.</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {posts.map(post => (
        <FeedPost key={post.id} post={post} currentUserId={currentUserId} />
      ))}
    </div>
  );
}
