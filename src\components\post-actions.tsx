
'use client';

import { 
    <PERSON><PERSON><PERSON><PERSON>,
    Repeat2,
    Heart,
    Bookmark,
    Share2,
    Loader2
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useState, useTransition } from "react";
import { voteAction, saveAction, repostAction } from "@/app/actions";
import { useToast } from "@/hooks/use-toast";
import type { Post } from "@/types/post";
import { cn } from "@/lib/utils";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "./ui/dropdown-menu";

export default function PostActions({ post: initialPost, currentUserId }: { post: Post, currentUserId: string | null }) {
    const [post, setPost] = useState(initialPost);
    const [isVotePending, startVoteTransition] = useTransition();
    const [isSavePending, startSaveTransition] = useTransition();
    const [isRepostPending, startRepostTransition] = useTransition();
    const { toast } = useToast();

    // If it's a repost, interactions apply to the repost document, 
    // but the displayed content comes from the original.
    const displayPost = post.isRepost ? post.originalPost! : post;
    const interactionPost = post;

    const handleLike = () => {
        if (!currentUserId) {
            toast({ variant: "destructive", title: "Please log in to like posts." });
            return;
        }
        startVoteTransition(async () => {
            const isLiked = interactionPost.userVote === 'up';
            const originalVotes = interactionPost.votes;
            
            const optimisticPost = { 
                ...post, 
                userVote: isLiked ? null : 'up',
                votes: originalVotes + (isLiked ? -1 : 1)
            };
            setPost(optimisticPost);

            const result = await voteAction(interactionPost.id, 'up');
            if (!result.success) {
                setPost(initialPost);
                toast({ variant: "destructive", title: "Action failed", description: result.message });
            }
        });
    };

    const handleSave = () => {
        if (!currentUserId) {
            toast({ variant: "destructive", title: "Please log in to save posts." });
            return;
        }
        startSaveTransition(async () => {
            const optimisticSaved = !interactionPost.isSaved;
            const optimisticPost = { ...post, isSaved: optimisticSaved };
            setPost(optimisticPost);
            
            const result = await saveAction(interactionPost.id, optimisticSaved);
            if (!result.success) {
                setPost(initialPost);
                toast({ variant: "destructive", title: "Action failed", description: result.message });
            }
        });
    };
  
    const handleShare = () => {
        const postUrl = `${window.location.origin}/post/${displayPost.id}`;
        navigator.clipboard.writeText(postUrl);
        toast({ title: "Link Copied!", description: "Post URL has been copied to your clipboard." });
    };
  
    const handleRepost = () => {
        if (!currentUserId) {
            toast({ variant: "destructive", title: "Please log in to repost." });
            return;
        }
        startRepostTransition(async () => {
            const result = await repostAction(displayPost.id);
            if (result.success) {
                toast({ title: "Post Reposted!" });
            } else {
                toast({ variant: "destructive", title: "Repost Failed", description: result.message });
            }
        });
    }

    return (
        <div className="flex justify-around py-2">
            <Button variant="ghost" size="icon" className="text-muted-foreground hover:text-blue-500 hover:bg-blue-500/10 rounded-full">
                <MessageCircle className="h-5 w-5" />
            </Button>
            
            <DropdownMenu>
                <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon" disabled={isRepostPending} className="text-muted-foreground hover:text-green-500 hover:bg-green-500/10 rounded-full">
                         {isRepostPending ? <Loader2 className="h-5 w-5 animate-spin"/> : <Repeat2 className="h-5 w-5" />}
                    </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="border-2 border-black">
                    <DropdownMenuItem onSelect={handleRepost} disabled={post.isRepost}>
                        <Repeat2 className="mr-2 h-4 w-4" /> Repost
                    </DropdownMenuItem>
                </DropdownMenuContent>
            </DropdownMenu>

            <Button variant="ghost" size="icon" onClick={handleLike} disabled={isVotePending} className={cn("text-muted-foreground hover:text-red-500 hover:bg-red-500/10 rounded-full", interactionPost.userVote === 'up' && 'text-red-500')}>
                <Heart className={cn("h-5 w-5", interactionPost.userVote === 'up' && 'fill-current')} />
            </Button>

            <Button variant="ghost" size="icon" onClick={handleSave} disabled={isSavePending} className={cn("text-muted-foreground hover:text-yellow-500 hover:bg-yellow-500/10 rounded-full", interactionPost.isSaved && 'text-yellow-500')}>
                <Bookmark className={cn("h-5 w-5", interactionPost.isSaved && 'fill-current')} />
            </Button>

            <Button variant="ghost" size="icon" onClick={handleShare} className="text-muted-foreground hover:text-blue-500 hover:bg-blue-500/10 rounded-full">
                <Share2 className="h-5 w-5" />
            </Button>
        </div>
    );
}
