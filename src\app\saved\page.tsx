
import AppSidebar from "@/components/app-sidebar";
import { Menu } from "lucide-react";
import { Sheet, Sheet<PERSON>ontent, Sheet<PERSON>rigger, SheetTitle } from "@/components/ui/sheet";
import { <PERSON><PERSON> } from "@/components/ui/button";
import PageLayout from "@/components/page-layout";
import { getSession } from "@/lib/session";
import { AlertTriangle, Bookmark } from "lucide-react";
import SavedPostsClient from "@/components/saved-posts-client";
import { getSavedPostsForUser } from "@/lib/firestore";

function AppHeader() {
  return (
    <header className="flex h-16 items-center gap-4 border-b-2 border-black bg-background px-6">
        <Sheet>
            <SheetTrigger asChild>
              <Button
                variant="outline"
                size="icon"
                className="shrink-0 md:hidden border-2 border-black"
              >
                <Menu className="h-5 w-5" />
                <span className="sr-only">Toggle navigation menu</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="p-0 border-r-2 border-black bg-primary">
                <SheetTitle className="sr-only">Navigation Menu</SheetTitle>
                <AppSidebar />
            </SheetContent>
        </Sheet>
        <div className="text-2xl font-bold font-headline flex items-center gap-2">
            <Bookmark />
            <span>Saved Posts</span>
        </div>
    </header>
  );
}

export default async function SavedPage() {
    const session = await getSession();
    
    if (!session) {
        return (
             <PageLayout>
                <AppHeader />
                <main className="flex flex-1 flex-col p-6 bg-background items-center justify-center">
                    <div className="flex flex-col items-center justify-center text-center text-muted-foreground p-8 rounded-lg border-2 border-dashed">
                        <AlertTriangle className="h-12 w-12 mb-4" />
                        <h3 className="text-xl font-bold font-headline">Authentication Required</h3>
                        <p className="mt-2">Please sign in to view your saved posts.</p>
                    </div>
                </main>
            </PageLayout>
        );
    }

    const savedPosts = await getSavedPostsForUser(session.uid);

    return (
        <PageLayout>
            <AppHeader />
            <main className="flex flex-1 flex-col p-4 md:p-6 bg-background">
                <SavedPostsClient posts={savedPosts} currentUserId={session.uid} />
            </main>
        </PageLayout>
    );
}
