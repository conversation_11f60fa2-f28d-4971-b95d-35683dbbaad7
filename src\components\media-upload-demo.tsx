'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import MediaUpload from './media-upload';
import PostMedia from './post-media';
import CorsTest from './cors-test';
import type { MediaItem } from '@/types/post';
import { formatFileSize } from '@/lib/image-compression';

export default function MediaUploadDemo() {
  const [media, setMedia] = useState<MediaItem[]>([]);
  const [showPreview, setShowPreview] = useState(false);

  const handleClear = () => {
    setMedia([]);
    setShowPreview(false);
  };

  const totalOriginalSize = media.reduce((sum, item) => sum + item.originalSize, 0);
  const totalCompressedSize = media.reduce((sum, item) => sum + item.fileSize, 0);
  const totalSavings = totalOriginalSize > 0 ? Math.round((1 - totalCompressedSize / totalOriginalSize) * 100) : 0;

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card className="border-2 border-black shadow-[4px_4px_0px_black]">
        <CardHeader>
          <CardTitle className="font-headline text-2xl">Media Upload Feature Demo</CardTitle>
          <CardDescription>
            Test the comprehensive media upload functionality with image compression, 
            drag-and-drop, progress tracking, and Firebase Storage integration.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* CORS Test Section */}
          <CorsTest />

          {/* Upload Section */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Upload Images</h3>
            <MediaUpload
              onMediaChange={setMedia}
              maxFiles={4}
              disabled={false}
            />
          </div>

          {/* Statistics */}
          {media.length > 0 && (
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card className="border border-gray-200">
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-blue-600">{media.length}</div>
                  <div className="text-sm text-gray-600">Images Uploaded</div>
                </CardContent>
              </Card>
              
              <Card className="border border-gray-200">
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-green-600">{formatFileSize(totalOriginalSize)}</div>
                  <div className="text-sm text-gray-600">Original Size</div>
                </CardContent>
              </Card>
              
              <Card className="border border-gray-200">
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-purple-600">{formatFileSize(totalCompressedSize)}</div>
                  <div className="text-sm text-gray-600">Compressed Size</div>
                </CardContent>
              </Card>
              
              <Card className="border border-gray-200">
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-orange-600">{totalSavings}%</div>
                  <div className="text-sm text-gray-600">Space Saved</div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Media Details */}
          {media.length > 0 && (
            <div>
              <h3 className="text-lg font-semibold mb-3">Upload Details</h3>
              <div className="space-y-2">
                {media.map((item, index) => (
                  <div key={item.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border">
                    <div className="flex items-center space-x-3">
                      <Badge variant="outline">{index + 1}</Badge>
                      <div>
                        <p className="font-medium text-sm">{item.fileName}</p>
                        <p className="text-xs text-gray-500">
                          {item.width}×{item.height} • {item.mimeType}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant="secondary" className="text-xs">
                        {formatFileSize(item.originalSize)} → {formatFileSize(item.fileSize)}
                      </Badge>
                      <Badge variant="outline" className="text-xs">
                        -{Math.round((1 - item.fileSize / item.originalSize) * 100)}%
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Preview Section */}
          {media.length > 0 && (
            <div>
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-lg font-semibold">Media Preview</h3>
                <div className="space-x-2">
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => setShowPreview(!showPreview)}
                  >
                    {showPreview ? 'Hide' : 'Show'} Preview
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={handleClear}
                  >
                    Clear All
                  </Button>
                </div>
              </div>
              
              {showPreview && (
                <Card className="border border-gray-200">
                  <CardContent className="p-4">
                    <PostMedia media={media} />
                  </CardContent>
                </Card>
              )}
            </div>
          )}

          {/* Feature Highlights */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Feature Highlights</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <h4 className="font-medium text-green-600">✅ Implemented Features</h4>
                <ul className="text-sm space-y-1 text-gray-600">
                  <li>• Drag-and-drop file upload</li>
                  <li>• Client-side image compression</li>
                  <li>• Real-time progress tracking</li>
                  <li>• Multiple file format support</li>
                  <li>• Firebase Storage integration</li>
                  <li>• Responsive image display</li>
                  <li>• Lightbox modal viewing</li>
                  <li>• File size optimization</li>
                </ul>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium text-blue-600">🔧 Technical Details</h4>
                <ul className="text-sm space-y-1 text-gray-600">
                  <li>• Max 4 images per upload</li>
                  <li>• 10MB file size limit</li>
                  <li>• 80% compression quality</li>
                  <li>• 1920px max dimensions</li>
                  <li>• JPEG, PNG, WebP, GIF support</li>
                  <li>• Secure Firebase Storage</li>
                  <li>• CDN-delivered images</li>
                  <li>• Mobile-responsive design</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
