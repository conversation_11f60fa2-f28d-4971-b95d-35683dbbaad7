
"use server";

import { generateHypotheses } from "@/ai/flows/genesis-hypothesis-generation";
import { stateOfTheFieldSynthesis } from "@/ai/flows/state-of-the-field-synthesis";
import { factCheck } from "@/ai/flows/fact-checking";
import { rankFeedPosts } from "@/ai/flows/rank-feed-posts";
import { z } from "zod";
import { 
    updateUserProfile, 
    initializeUserProfile as initializeUserProfileInDb, 
    createPost as createPostInDb,
    getRawPost,
    getUserProfile,
    handleVote,
    handleSave,
    handleFollow,
    addComment,
    handleCommentVote,
    handleCommentSave,
    getPost,
    getComment,
    getRawComment,
    createSpace as createSpaceInDb,
} from "@/lib/firestore";
import { revalidatePath } from "next/cache";
import type { UserProfile } from "@/types/user";
import { getSession } from "@/lib/session";
import type { Post } from "@/types/post";
import { genesisExplainer } from "@/ai/flows/genesis-explainer";
import { redirect } from "next/navigation";


const hypothesisSchema = z.object({
  topic: z.string().min(10, { message: "Topic must be at least 10 characters long." }),
});

export async function generateHypothesesAction(prevState: any, formData: FormData) {
  const validatedFields = hypothesisSchema.safeParse({
    topic: formData.get('topic'),
  });

  if (!validatedFields.success) {
    return {
      message: 'Invalid topic.',
      errors: validatedFields.error.flatten().fieldErrors,
      data: null,
    };
  }

  try {
    const result = await generateHypotheses({ topic: validatedFields.data.topic });
    return { message: 'success', data: result, errors: null };
  } catch (error) {
    console.error(error);
    return { message: 'An error occurred while generating hypotheses.', data: null, errors: null };
  }
}


const synthesisSchema = z.object({
  topic: z.string().min(10, { message: "Topic must be at least 10 characters long." }),
});

export async function stateOfTheFieldSynthesisAction(prevState: any, formData: FormData) {
    const validatedFields = synthesisSchema.safeParse({
        topic: formData.get('topic'),
    });

    if (!validatedFields.success) {
        return {
            message: 'Invalid topic.',
            errors: validatedFields.error.flatten().fieldErrors,
            data: null,
        };
    }

    try {
        const result = await stateOfTheFieldSynthesis({ topic: validatedFields.data.topic });
        return { message: 'success', data: result, errors: null };
    } catch (error) {
        console.error(error);
        return { message: 'An error occurred during synthesis.', data: null, errors: null };
    }
}


const factCheckSchema = z.object({
    statement: z.string().min(10, { message: "Statement must be at least 10 characters long." }),
});

export async function factCheckAction(prevState: any, formData: FormData) {
    const validatedFields = factCheckSchema.safeParse({
        statement: formData.get('statement'),
    });

    if (!validatedFields.success) {
        return {
            message: 'Invalid statement.',
            errors: validatedFields.error.flatten().fieldErrors,
            data: null,
        };
    }

    try {
        const result = await factCheck({ statement: validatedFields.data.statement });
        return { message: 'success', data: result, errors: null };
    } catch (error) {
        console.error(error);
        return { message: 'An error occurred during fact-checking.', data: null, errors: null };
    }
}

const rankPostsSchema = z.object({
  posts: z.array(z.any()),
});

export async function rankPostsAction(prevState: any, formData: FormData) {
  const posts = JSON.parse(formData.get('posts') as string);

  const validatedFields = rankPostsSchema.safeParse({ posts });

  if (!validatedFields.success) {
    return {
      message: 'Invalid posts data.',
      data: null,
      errors: validatedFields.error.flatten().fieldErrors,
    };
  }
  
  const user = {
    profile: "Senior Policy Advisor with expertise in renewable energy and international climate agreements. Skills in data analysis, public speaking, and legislative drafting.",
    interests: "Carbon capture technology, sustainable urban development, international relations.",
    activity: "Commented on a debate about nuclear fusion. Published an article on the economic impacts of the Paris Agreement.",
  };

  try {
    const result = await rankFeedPosts({ posts: validatedFields.data.posts, user });
    return { message: 'success', data: result.rankedPosts, errors: null };
  } catch (error) {
    console.error(error);
    return { message: 'An error occurred while ranking posts.', data: posts, errors: null };
  }
}

const MOCK_USER_ID = "test-user-123";

const profileUpdateSchema = z.object({
    name: z.string().min(2),
    title: z.string().min(5),
    career: z.array(z.object({
        role: z.string(),
        company: z.string(),
        period: z.string(),
    })).optional(),
    skills: z.array(z.object({
        name: z.string(),
        endorsements: z.number(),
    })).optional(),
});

export async function updateUserProfileAction(prevState: any, formData: FormData) {
    const rawData = formData.get('profileData');
    if (!rawData) {
        return { message: 'No data submitted.' };
    }

    try {
        const data = JSON.parse(rawData as string);
        const validatedFields = profileUpdateSchema.safeParse(data);

        if (!validatedFields.success) {
            console.log(validatedFields.error.flatten().fieldErrors);
            return {
                message: 'Invalid profile data. Please check the fields.',
                errors: validatedFields.error.flatten().fieldErrors,
            };
        }

        await updateUserProfile(MOCK_USER_ID, validatedFields.data);
        revalidatePath('/profile');
        return { message: 'success' };
    } catch (error) {
        console.error(error);
        return { message: 'An error occurred while updating the profile.' };
    }
}

export async function initializeUserProfile(userId: string, data: { email: string, name?: string | null, avatarUrl?: string | null }) {
    try {
        await initializeUserProfileInDb(userId, data);
        return { success: true, message: "User profile handled." };
    } catch (error) {
        console.error("Error from initializeUserProfile action:", error);
        return { success: false, message: "Failed to initialize user profile." };
    }
}

const createPostSchema = z.object({
  content: z.string().min(10, { message: "Post must be at least 10 characters long." }).max(500, { message: "Post cannot exceed 500 characters."}),
  space: z.string().min(1, { message: "Please select a space for your post."}),
});

export async function createPostAction(prevState: any, formData: FormData) {
    const validatedFields = createPostSchema.safeParse({
        content: formData.get('content'),
        space: formData.get('space'),
    });

    if (!validatedFields.success) {
        return {
            message: 'Invalid post data.',
            errors: validatedFields.error.flatten().fieldErrors,
        };
    }

    try {
        const session = await getSession();
        if (!session) {
            return { message: 'You must be logged in to create a post.' };
        }

        const userId = session.uid;
        const userProfile = await getUserProfile(userId);

        if (!userProfile) {
            return { message: 'User profile not found.' };
        }

        const postData: Omit<Post, 'id' | 'createdAt' | 'votes' | 'commentsCount'> = {
            content: validatedFields.data.content,
            space: validatedFields.data.space,
            authorId: userId,
            authorName: userProfile.name,
            authorAvatar: userProfile.avatarUrl,
            votes: 0,
            commentsCount: 0
        };

        await createPostInDb(postData);

        revalidatePath('/dashboard');
        return { message: 'success' };

    } catch (error) {
        console.error("Error creating post:", error);
        return { message: 'An unexpected error occurred while creating the post.' };
    }
}

export async function voteAction(postId: string, voteType: 'up' | 'down') {
    try {
        const session = await getSession();
        if (!session) {
            return { success: false, message: "Authentication required." };
        }
        await handleVote(session.uid, postId, voteType);
        revalidatePath('/dashboard');
        revalidatePath(`/post/${postId}`);
        return { success: true };
    } catch (error) {
        console.error("Error in voteAction:", error);
        return { success: false, message: "An unexpected error occurred." };
    }
}

export async function saveAction(postId: string, isSaved: boolean) {
    try {
        const session = await getSession();
        if (!session) {
            return { success: false, message: "Authentication required." };
        }
        await handleSave(session.uid, postId, isSaved);
        revalidatePath('/dashboard');
        revalidatePath('/saved');
        revalidatePath(`/post/${postId}`);
        return { success: true };
    } catch (error) {
        console.error("Error in saveAction:", error);
        return { success: false, message: "An unexpected error occurred." };
    }
}

export async function followAction(targetUserId: string, isFollowing: boolean) {
    try {
        const session = await getSession();
        if (!session) {
            return { success: false, message: "Authentication required." };
        }
        await handleFollow(session.uid, targetUserId, isFollowing);
        revalidatePath('/dashboard');
        revalidatePath('/profile');
        return { success: true };
    } catch (error) {
        console.error("Error in followAction:", error);
        return { success: false, message: "An unexpected error occurred." };
    }
}

export async function repostAction(originalPostId: string, quoteContent: string = '') {
    try {
        const session = await getSession();
        if (!session) {
            return { success: false, message: "Authentication required." };
        }

        const userProfile = await getUserProfile(session.uid);
        if (!userProfile) {
            return { success: false, message: "User profile not found." };
        }

        const originalPost = await getRawPost(originalPostId);
        if (!originalPost) {
            return { success: false, message: "Original post not found." };
        }
        
        if(originalPost.isRepost) {
            return { success: false, message: "Cannot repost a repost." };
        }

        const repostData: Partial<Post> = {
            content: quoteContent,
            space: originalPost.space,
            authorId: session.uid,
            authorName: userProfile.name,
            authorAvatar: userProfile.avatarUrl,
            isRepost: true,
            originalPost: originalPost,
            votes: 0,
            commentsCount: 0
        };

        await createPostInDb(repostData as Omit<Post, 'id' | 'createdAt'>);

        revalidatePath('/dashboard');
        revalidatePath(`/post/${originalPost.id}`);
        return { success: true };

    } catch (error) {
        console.error("Error creating repost:", error);
        return { success: false, message: 'An unexpected error occurred while reposting.' };
    }
}

export async function repostCommentAction(postId: string, commentId: string, quoteContent: string = '') {
    try {
        const session = await getSession();
        if (!session) {
            return { success: false, message: "Authentication required." };
        }

        const userProfile = await getUserProfile(session.uid);
        if (!userProfile) {
            return { success: false, message: "User profile not found." };
        }

        const originalComment = await getRawComment(postId, commentId);
        if (!originalComment) {
            return { success: false, message: "Original comment not found." };
        }
        
        const originalPost = await getRawPost(originalComment.postId);
        if (!originalPost) {
            return { success: false, message: "Original post for comment not found." };
        }

        const repostData: Partial<Post> = {
            content: quoteContent,
            space: originalPost.space,
            authorId: session.uid,
            authorName: userProfile.name,
            authorAvatar: userProfile.avatarUrl,
            isRepost: true,
            originalComment: originalComment,
            votes: 0,
            commentsCount: 0
        };

        await createPostInDb(repostData as Omit<Post, 'id' | 'createdAt'>);

        revalidatePath('/dashboard');
        revalidatePath(`/post/${postId}/comment/${commentId}`);
        return { success: true };

    } catch (error) {
        console.error("Error creating comment repost:", error);
        return { success: false, message: 'An unexpected error occurred while reposting the comment.' };
    }
}

const addCommentSchema = z.object({
  content: z.string().min(1, "Comment cannot be empty.").max(500, "Comment is too long."),
  postId: z.string(),
  parentCommentId: z.string().optional(),
});

export async function addCommentAction(prevState: any, formData: FormData) {
    const validatedFields = addCommentSchema.safeParse({
        content: formData.get('content'),
        postId: formData.get('postId'),
        parentCommentId: formData.get('parentCommentId') || undefined,
    });

    if (!validatedFields.success) {
        return {
            message: 'Invalid comment data.',
            errors: validatedFields.error.flatten().fieldErrors,
        };
    }
    
    try {
        const session = await getSession();
        if (!session) {
            return { success: false, message: "Authentication required." };
        }

        const userProfile = await getUserProfile(session.uid);
         if (!userProfile) {
            return { message: 'User profile not found.' };
        }
        
        const { postId, content, parentCommentId } = validatedFields.data;

        const userCommentId = await addComment(
            postId, 
            {
                content,
                authorId: session.uid,
                authorName: userProfile.name,
                authorAvatar: userProfile.avatarUrl,
            },
            parentCommentId || null
        );

        if (content.toLowerCase().startsWith('@genesis')) {
            const question = content.substring(8).trim();
            
            let context = '';
            const originalPost = await getPost(postId, null);

            if (parentCommentId) {
                const parentComment = await getComment(postId, parentCommentId);
                context = `The user is replying to the following comment: "${parentComment?.content}". The original post is about: "${originalPost?.content}"`;
            } else {
                context = `The user is replying to the following post: "${originalPost?.content}"`;
            }

            const aiResponse = await genesisExplainer({
                userQuestion: question,
                threadContext: context,
            });

            await addComment(
                postId,
                {
                    content: aiResponse.explanation,
                    authorId: 'genesis-ai-assistant',
                    authorName: 'Genesis',
                    authorAvatar: 'https://placehold.co/200x200.png?text=G',
                },
                userCommentId
            );
        }


        revalidatePath(`/post/${postId}`);
        if (parentCommentId) {
            revalidatePath(`/post/${postId}/comment/${parentCommentId}`);
        }
        revalidatePath(`/post/${postId}/comment/${userCommentId}`);

        return { message: 'success' };

    } catch (error) {
        console.error("Error adding comment:", error);
        return { message: 'An unexpected error occurred.' };
    }
}


export async function commentVoteAction(postId: string, commentId: string, voteType: 'up' | 'down') {
    try {
        const session = await getSession();
        if (!session) {
            return { success: false, message: "Authentication required." };
        }
        await handleCommentVote(session.uid, postId, commentId, voteType);
        revalidatePath(`/post/${postId}`);
        revalidatePath(`/post/${postId}/comment/${commentId}`);
        return { success: true };
    } catch (error) {
        console.error("Error in commentVoteAction:", error);
        return { success: false, message: "An unexpected error occurred." };
    }
}

export async function commentSaveAction(postId: string, commentId: string, isSaved: boolean) {
    try {
        const session = await getSession();
        if (!session) {
            return { success: false, message: "Authentication required." };
        }
        await handleCommentSave(session.uid, postId, commentId, isSaved);
        revalidatePath(`/post/${postId}`);
        revalidatePath(`/post/${postId}/comment/${commentId}`);
        return { success: true };
    } catch (error) {
        console.error("Error in commentSaveAction:", error);
        return { success: false, message: "An unexpected error occurred." };
    }
}

const spaceFormSchema = z.object({
  name: z.string().min(1, "Space name is required.").max(50, "Name cannot exceed 50 characters."),
  description: z.string().min(100, "Description must be at least 100 characters.").max(1000, "Description cannot exceed 1000 characters."),
  category: z.string().min(1, "Please select a category."),
  tags: z.array(z.string()).max(10, "You can add up to 10 tags.").default([]),
  
  contentTypes: z.object({
    text: z.boolean().default(true),
    documents: z.boolean().default(false),
    images: z.boolean().default(false),
    video: z.boolean().default(false),
    data: z.boolean().default(false),
    code: z.boolean().default(false),
    claims: z.boolean().default(false),
    polls: z.boolean().default(false),
  }).default({ text: true }),
  discussionFormat: z.enum(['threaded', 'linear', 'q&a', 'debate']).default('threaded'),
  contentApproval: z.boolean().default(false),
  versioning: z.boolean().default(false),

  tools: z.object({
    projectManagement: z.boolean().default(false),
    fileStorage: z.boolean().default(false),
    codeSandbox: z.boolean().default(false),
    whiteboard: z.boolean().default(false),
    conferencing: z.boolean().default(false),
    calendar: z.boolean().default(false),
  }).default({}),

  visibility: z.enum(['public', 'private', 'secret']).default('public'),
  publicJoinOptions: z.enum(['open', 'request']).default('open'),
  privateJoinOptions: z.enum(['request', 'invite']).default('request'),
  secretJoinOptions: z.enum(['invite', 'link']).default('invite'),
  requiredAuthLevel: z.string().default('Community'),
});

export async function createSpaceAction(prevState: any, formData: FormData) {
    const session = await getSession();
    if (!session) {
        return { message: 'Authentication required.' };
    }
    
    const data = JSON.parse(formData.get('spaceData') as string);
    const validatedFields = spaceFormSchema.safeParse(data);

    if (!validatedFields.success) {
        return {
            message: 'Invalid data submitted.',
            errors: validatedFields.error.flatten().fieldErrors
        };
    }
    
    const userProfile = await getUserProfile(session.uid);

    try {
        await createSpaceInDb({
            ...validatedFields.data,
            creatorId: session.uid,
            creatorName: userProfile.name,
            creatorAvatar: userProfile.avatarUrl,
            memberCount: 1,
        });

        revalidatePath('/spaces/discover');
        revalidatePath('/dashboard');
        
    } catch (error) {
        console.error("Error creating space:", error);
        return { message: "An unexpected error occurred while creating the space." };
    }
    
    redirect('/spaces/discover');
}
