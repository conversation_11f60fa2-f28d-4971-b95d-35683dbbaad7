'use server';

/**
 * @fileOverview Ranks feed posts based on user profile and interests.
 *
 * - rankFeedPosts - A function that ranks a list of posts for a user.
 * - RankFeedPostsInput - The input type for the rankFeedPosts function.
 * - RankFeedPostsOutput - The return type for the rankFeedPosts function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const PostSchema = z.object({
  id: z.number(),
  votes: z.number(),
  author: z.object({
    username: z.string(),
    avatar: z.string(),
  }),
  space: z.string(),
  time: z.string(),
  content: z.string(),
  comments: z.number(),
});

const UserProfileSchema = z.object({
    profile: z.string().describe("User's professional profile and expertise."),
    interests: z.string().describe("Topics the user is interested in."),
    activity: z.string().describe("User's recent activity on the platform."),
});

const RankFeedPostsInputSchema = z.object({
  posts: z.array(PostSchema).describe('An array of posts to be ranked.'),
  user: UserProfileSchema.describe("The profile of the user for whom the feed is being generated.")
});
export type RankFeedPostsInput = z.infer<typeof RankFeedPostsInputSchema>;

const RankedPostSchema = PostSchema.extend({
    relevanceScore: z.number().describe('A score from 0 to 1 indicating the relevance of the post to the user.'),
    relevanceReasoning: z.string().describe('A brief explanation of why the post is considered relevant to the user.')
});

const RankFeedPostsOutputSchema = z.object({
  rankedPosts: z.array(RankedPostSchema).describe('The list of posts, sorted by relevance to the user.'),
});
export type RankFeedPostsOutput = z.infer<typeof RankFeedPostsOutputSchema>;

export async function rankFeedPosts(input: RankFeedPostsInput): Promise<RankFeedPostsOutput> {
  return rankFeedPostsFlow(input);
}

const prompt = ai.definePrompt({
  name: 'rankFeedPostsPrompt',
  input: {schema: RankFeedPostsInputSchema},
  output: {schema: RankFeedPostsOutputSchema},
  prompt: `You are an advanced AI algorithm for a professional social network called POLITICA. Your task is to rank a feed of posts for a specific user based on their profile, interests, and recent activity.

  Analyze the provided user information and the list of posts. For each post, you must determine a relevance score and provide a brief reasoning for the ranking.

  The user's information is as follows:
  - Profile: {{{user.profile}}}
  - Interests: {{{user.interests}}}
  - Recent Activity: {{{user.activity}}}

  Here is the list of posts you need to rank:
  {{#each posts}}
  - Post ID: {{id}}
  - Author: {{author.username}}
  - Content: "{{content}}"
  - Space: {{space}}
  - Votes: {{votes}}
  {{/each}}

  Instructions:
  1. Evaluate each post against the user's information.
  2. Assign a 'relevanceScore' from 0 (not relevant) to 1 (highly relevant).
  3. Provide a concise 'relevanceReasoning' for each score, explaining why it's a good recommendation for this specific user.
  4. Sort the final list of posts in descending order of 'relevanceScore'.
  5. Ensure your response is a valid JSON object conforming to the RankFeedPostsOutputSchema.
  `,
});

const rankFeedPostsFlow = ai.defineFlow(
  {
    name: 'rankFeedPostsFlow',
    inputSchema: RankFeedPostsInputSchema,
    outputSchema: RankFeedPostsOutputSchema,
  },
  async (input) => {
    const { output } = await prompt(input);
    return output!;
  }
);
