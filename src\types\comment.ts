
export interface Comment {
  id: string;
  postId: string; // ID of the parent post
  parentCommentId: string | null; // For nested replies
  content: string;
  authorId: string;
  authorName: string;
  authorAvatar: string;
  createdAt: string;
  votes: number;
  commentsCount: number;
  // Client-side properties
  replies?: Comment[];
  // User-specific properties added on the server
  userVote?: 'up' | 'down' | null;
  isSaved?: boolean;
}
