
"use client";

import { useFormStatus } from "react-dom";
import { useActionState, useEffect } from "react";
import { generateHypothesesAction } from "@/app/actions";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { <PERSON><PERSON>, Lightbulb, Loader2 } from "lucide-react";

const initialState = {
  message: "",
  data: null,
  errors: null,
};

function SubmitButton() {
  const { pending } = useFormStatus();
  return (
    <Button 
      type="submit" 
      aria-disabled={pending} 
      disabled={pending}
      className="bg-accent text-accent-foreground border-2 border-black shadow-[2px_2px_0px_black] hover:shadow-[4px_4px_0px_black] active:shadow-[1px_1px_0px_black] transition-all w-full md:w-auto"
    >
      {pending ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Bot className="mr-2 h-4 w-4" />}
      {pending ? "Generating..." : "Generate Hypotheses"}
    </Button>
  );
}

export default function HypothesisGeneratorClient() {
  const [state, formAction] = useActionState(generateHypothesesAction, initialState);
  const { toast } = useToast();

  useEffect(() => {
    if (state.message && state.message !== 'success') {
      toast({
        variant: "destructive",
        title: "Error",
        description: state.message,
      });
    }
  }, [state, toast]);

  return (
    <div className="grid gap-6">
      <Card className="border-2 border-black shadow-[4px_4px_0px_black] rounded-lg">
        <CardHeader>
          <CardTitle className="font-headline">Enter a Research Topic</CardTitle>
          <CardDescription>
            Describe a field of study, a scientific problem, or a concept, and the "Genesis" engine will generate novel hypotheses and potential research directions.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form action={formAction} className="space-y-4">
            <Textarea
              name="topic"
              placeholder="e.g., 'The impact of social media on political polarization in developing nations'"
              className="min-h-[100px] border-2 border-black focus:shadow-[2px_2px_0px_black] transition-shadow"
            />
             {state.errors?.topic && <p className="text-sm font-medium text-destructive">{state.errors.topic}</p>}
            <SubmitButton />
          </form>
        </CardContent>
      </Card>

      {state.data && (
        <div className="grid gap-6 lg:grid-cols-2">
          <Card className="border-2 border-black shadow-[4px_4px_0px_black] rounded-lg">
            <CardHeader>
              <CardTitle className="font-headline">Generated Hypotheses</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {state.data.hypotheses.map((h, i) => (
                <div key={i} className="flex gap-3 items-start p-3 bg-muted/50 rounded-md">
                   <Lightbulb className="h-5 w-5 text-accent flex-shrink-0 mt-1" />
                  <p>{h}</p>
                </div>
              ))}
            </CardContent>
          </Card>
          <Card className="border-2 border-black shadow-[4px_4px_0px_black] rounded-lg">
            <CardHeader>
              <CardTitle className="font-headline">Research Directions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <ul className="list-disc pl-5 space-y-2">
                {state.data.researchDirections.map((rd, i) => (
                  <li key={i}>{rd}</li>
                ))}
              </ul>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
