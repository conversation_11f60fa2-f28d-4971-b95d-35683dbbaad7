
'use client';

import AuthForm from "@/components/auth/auth-form";
import { Logo } from "@/components/logo";
import Link from "next/link";
import { useEffect, useState } from "react";
import { getAuth, isSignInWithEmailLink, signInWithEmailLink } from "firebase/auth";
import firebaseApp from "@/lib/firebase";
import { useToast } from "@/hooks/use-toast";
import { useRouter } from "next/navigation";
import { initializeUserProfile } from "./actions";
import { Loader2 } from "lucide-react";

export default function AuthPage() {
  const auth = getAuth(firebaseApp);
  const { toast } = useToast();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [isVerifying, setIsVerifying] = useState(false);

  useEffect(() => {
    // This effect only runs on the client, after hydration.
    // It checks if the page URL is a sign-in link from Firebase.
    const handleEmailLinkLogin = async () => {
      if (isSignInWithEmailLink(auth, window.location.href)) {
        setIsVerifying(true);
        let email = window.localStorage.getItem('emailForSignIn');
        if (!email) {
          toast({ variant: "destructive", title: "Login Failed", description: "Your session may have expired or you opened the link in a different browser. Please try signing in again." });
          setIsVerifying(false);
          router.replace('/'); // Clean the URL
          return;
        }

        try {
          // Complete the sign-in process
          const result = await signInWithEmailLink(auth, email, window.location.href);
          window.localStorage.removeItem('emailForSignIn');
          
          const user = result.user;
          if (user) {
            // Ensure a profile exists for this user on our backend
            await initializeUserProfile(user.uid, {
              email: user.email!,
              name: user.displayName,
              avatarUrl: user.photoURL
            });

            // Get the ID token and send it to our API to create a session cookie
            const token = await user.getIdToken();
            const response = await fetch('/api/login', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ token }),
            });

            if (response.ok) {
                toast({ title: "Login Successful", description: "Welcome back!" });
                // Use window.location.assign to ensure a full page reload and middleware re-evaluation
                window.location.assign('/dashboard');
            } else {
                const errorData = await response.json();
                throw new Error(errorData.error || "API login failed.");
            }
          } else {
             toast({ variant: "destructive", title: "Login Failed", description: "Could not retrieve user information." });
             setIsVerifying(false);
          }
        } catch (error: any) {
          toast({ variant: "destructive", title: "Login Failed", description: error.message });
          router.replace('/'); // Clean the URL
          setIsVerifying(false);
        }
      } else {
        // If it's not a sign-in link, we're done loading.
        setIsLoading(false);
      }
    };
    
    handleEmailLinkLogin();
  }, [auth, router, toast]);

  // Show a loading spinner while verifying the email link or just on initial page load.
  if (isLoading || isVerifying) {
    return (
      <div className="flex min-h-screen w-full flex-col items-center justify-center bg-background p-4">
        <Loader2 className="h-8 w-8 animate-spin" />
        <p className="mt-4 text-muted-foreground">{isVerifying ? "Verifying your login..." : "Loading..."}</p>
      </div>
    );
  }

  // Once loading is complete, show the main authentication form.
  return (
    <div className="flex min-h-screen w-full flex-col items-center justify-center bg-background p-4">
      <div className="absolute top-6 left-6">
        <Link href="/" aria-label="Back to Home">
          <Logo />
        </Link>
      </div>
      <div className="w-full max-w-md">
        <AuthForm />
      </div>
    </div>
  );
}
