
import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import AppSidebar from "@/components/app-sidebar";
import { Badge } from "@/components/ui/badge";
import { Briefcase, ThumbsUp, ShieldCheck, Star, Award, Users, Menu } from "lucide-react";
import { Sheet, SheetTrigger, SheetContent, SheetTitle } from "@/components/ui/sheet";
import PageLayout from "@/components/page-layout";
import { getUserProfile } from '@/lib/firestore';
import type { UserProfile } from '@/types/user';
import EditProfileForm from '@/components/edit-profile-form';

// For now, we'll use a hardcoded user ID. In a real app, this would come from the auth session.
const MOCK_USER_ID = "test-user-123";

function AppHeader() {
  return (
    <header className="flex h-16 items-center gap-4 border-b-2 border-black bg-background px-6">
        <Sheet>
            <SheetTrigger asChild>
              <Button
                variant="outline"
                size="icon"
                className="shrink-0 md:hidden border-2 border-black"
              >
                <Menu className="h-5 w-5" />
                <span className="sr-only">Toggle navigation menu</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="p-0 border-r-2 border-black bg-primary">
                <SheetTitle className="sr-only">Navigation Menu</SheetTitle>
                <AppSidebar />
            </SheetContent>
        </Sheet>
        <div className="flex-1 flex justify-between items-center">
            <div className="text-2xl font-bold font-headline">Profile</div>
        </div>
    </header>
  );
}

// A helper to map icon strings to Lucide components
const achievementIcons: { [key: string]: React.ElementType } = {
  Award,
  ShieldCheck,
  Users,
};

export default async function ProfilePage() {
    const userProfile: UserProfile = await getUserProfile(MOCK_USER_ID);

    return (
        <PageLayout>
            <AppHeader />
            <main className="flex flex-1 flex-col gap-6 p-4 md:p-6 bg-background">
                <div className="flex flex-col sm:flex-row items-center sm:items-start gap-6">
                    <Avatar className="h-24 w-24 md:h-32 md:w-32 border-4 border-black">
                        <AvatarImage src={userProfile.avatarUrl} data-ai-hint="professional headshot" />
                        <AvatarFallback>{userProfile.name.charAt(0)}</AvatarFallback>
                    </Avatar>
                    <div className="grid gap-2 text-center sm:text-left flex-1">
                        <h1 className="text-3xl md:text-4xl font-bold font-headline">{userProfile.name}</h1>
                        <p className="text-muted-foreground text-base md:text-lg">{userProfile.title}</p>
                        <div className="flex items-center justify-center sm:justify-start gap-2 mt-2 flex-wrap">
                            <Badge variant="outline" className="text-primary flex items-center gap-1 text-sm border-2 border-black">
                                <ShieldCheck className="h-4 w-4" />
                                {userProfile.verification} Verification
                            </Badge>
                            <Badge variant="secondary" className="flex items-center gap-1 text-sm border-2 border-black">
                                <Star className="h-4 w-4" />
                                Reputation: {userProfile.reputation.toLocaleString()}
                            </Badge>
                        </div>
                    </div>
                    <EditProfileForm profile={userProfile} />
                </div>

                <div className="grid gap-6 grid-cols-1 lg:grid-cols-3">
                    <Card className="border-2 border-black rounded-lg shadow-[4px_4px_0px_#000]">
                        <CardHeader>
                            <CardTitle className="font-bold flex items-center justify-between">
                                <span>Career Tracking</span>
                            </CardTitle>
                            <CardDescription>Professional journey and milestones.</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            {userProfile.career.map((job, index) => (
                                <div key={index} className="flex gap-4">
                                    <Briefcase className="h-6 w-6 text-muted-foreground mt-1" />
                                    <div>
                                        <p className="font-semibold">{job.role}</p>
                                        <p className="text-sm">{job.company}</p>
                                        <p className="text-xs text-muted-foreground">{job.period}</p>
                                    </div>
                                </div>
                            ))}
                        </CardContent>
                    </Card>

                    <Card className="border-2 border-black rounded-lg shadow-[4px_4px_0px_#000]">
                        <CardHeader>
                            <CardTitle className="font-bold">Skills & Endorsements</CardTitle>
                            <CardDescription>Recognized skills by the community.</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-3">
                            {userProfile.skills.map((skill, index) => (
                                <div key={index} className="flex justify-between items-center">
                                    <span>{skill.name}</span>
                                    <div className="flex items-center gap-2">
                                        <span className="text-sm font-semibold">{skill.endorsements.toLocaleString()}</span>
                                        <Button size="sm" variant="outline" className="border-2 border-black shadow-[2px_2px_0px_black] hover:bg-accent hover:text-accent-foreground"><ThumbsUp className="h-4 w-4"/></Button>
                                    </div>
                                </div>
                            ))}
                        </CardContent>
                    </Card>

                    <Card className="border-2 border-black rounded-lg shadow-[4px_4px_0px_#000]">
                        <CardHeader>
                            <CardTitle className="font-bold">Achievements</CardTitle>
                            <CardDescription>Badges and recognitions earned.</CardDescription>
                        </CardHeader>
                        <CardContent className="flex flex-wrap gap-4">
                            {userProfile.achievements.map((achievement, index) => {
                                const Icon = achievementIcons[achievement.icon] || Award;
                                return (
                                    <div key={index} className="flex flex-col items-center text-center gap-1">
                                        <Icon className="h-10 w-10 text-accent"/>
                                        <span className="text-xs font-medium">{achievement.name}</span>
                                    </div>
                                );
                            })}
                        </CardContent>
                    </Card>
                </div>
            </main>
        </PageLayout>
    );
}
