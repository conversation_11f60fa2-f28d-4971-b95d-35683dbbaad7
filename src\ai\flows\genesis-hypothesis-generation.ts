'use server';

/**
 * @fileOverview An AI agent for generating novel hypotheses and research directions.
 *
 * - generateHypotheses - A function that generates hypotheses for a given topic.
 * - GenesisHypothesisInput - The input type for the generateHypotheses function.
 * - GenesisHypothesisOutput - The return type for the generateHypotheses function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const GenesisHypothesisInputSchema = z.object({
  topic: z.string().describe('The research topic for which to generate hypotheses.'),
});
export type GenesisHypothesisInput = z.infer<typeof GenesisHypothesisInputSchema>;

const GenesisHypothesisOutputSchema = z.object({
  hypotheses: z.array(z.string()).describe('An array of novel hypotheses related to the research topic.'),
  researchDirections: z.array(z.string()).describe('Suggested research directions based on the generated hypotheses.'),
});
export type GenesisHypothesisOutput = z.infer<typeof GenesisHypothesisOutputSchema>;

export async function generateHypotheses(input: GenesisHypothesisInput): Promise<GenesisHypothesisOutput> {
  return genesisHypothesisGenerationFlow(input);
}

const prompt = ai.definePrompt({
  name: 'genesisHypothesisPrompt',
  input: {schema: GenesisHypothesisInputSchema},
  output: {schema: GenesisHypothesisOutputSchema},
  prompt: `You are an AI assistant designed to generate novel hypotheses and research directions for a given research topic.

  Topic: {{{topic}}}

  Generate a list of hypotheses related to the topic, and then suggest potential research directions based on those hypotheses.

  Format your response as a JSON object with "hypotheses" and "researchDirections" fields. Each field should contain an array of strings.
  `,
});

const genesisHypothesisGenerationFlow = ai.defineFlow(
  {
    name: 'genesisHypothesisGenerationFlow',
    inputSchema: GenesisHypothesisInputSchema,
    outputSchema: GenesisHypothesisOutputSchema,
  },
  async input => {
    const {output} = await prompt(input);
    return output!;
  }
);
