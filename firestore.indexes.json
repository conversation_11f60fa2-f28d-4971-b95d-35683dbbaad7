{"indexes": [{"collectionGroup": "spaces", "queryScope": "COLLECTION", "fields": [{"fieldPath": "visibility", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "space", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "comments", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "postId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}]}, {"collectionGroup": "follows", "queryScope": "COLLECTION", "fields": [{"fieldPath": "followerId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "saves", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}], "fieldOverrides": []}