
"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { getAuth, createUserWithEmailAndPassword, signInWithPopup, GoogleAuthProvider } from "firebase/auth";
import firebaseApp from "@/lib/firebase";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Loader2, UserPlus } from "lucide-react";
import { initializeUserProfile } from "@/app/actions";

const GoogleIcon = () => <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5"><path d="M15.545 6.558a9.42 9.42 0 0 1 .139 1.626c0 2.434-.87 4.492-2.384 5.885h.002a.75.75 0 0 0 .75-.75a.75.75 0 0 0-.75-.75a2.25 2.25 0 0 0-2.25 2.25c0 .532.12.9-.309 1.22-.429.32-1.036.42-1.59.42-.96 0-1.787-.19-2.553-.429a.75.75 0 0 0-1.085.75c.29.69.837 1.255 1.523 1.654a8.99 8.99 0 0 0 2.825.741c.905 0 1.77-.184 2.553-.429.776-.245 1.433-.636 2.008-1.135a7.01 7.01 0 0 0 2.16-5.885c0-1.582-.55-2.98-1.558-4.085z"></path><path d="M12 21a9 9 0 1 0 0-18 9 9 0 0 0 0 18z"></path></svg>;

const signupSchema = z
  .object({
    name: z.string().min(2, { message: "Name must be at least 2 characters." }),
    email: z.string().email({ message: "Invalid email address." }),
    password: z.string().min(6, { message: "Password must be at least 6 characters." }),
    confirmPassword: z.string(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match.",
    path: ["confirmPassword"],
  });

type SignupFormValues = z.infer<typeof signupSchema>;

export default function SignupForm() {
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const auth = getAuth(firebaseApp);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<SignupFormValues>({
    resolver: zodResolver(signupSchema),
  });

  const onSubmit = async (data: SignupFormValues) => {
    setIsLoading(true);
    try {
      const userCredential = await createUserWithEmailAndPassword(auth, data.email, data.password);
      const user = userCredential.user;
      if (user) {
        await initializeUserProfile(user.uid, { email: data.email, name: data.name });
        const token = await user.getIdToken();
        
        const response = await fetch('/api/login', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ token }),
        });

        if (response.ok) {
            toast({ title: "Account Created", description: "Welcome to POLITICA! We're glad to have you." });
            window.location.assign('/dashboard');
        } else {
            const errorData = await response.json();
            throw new Error(errorData.error || "API login failed.");
        }
      }
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Sign Up Failed",
        description: error.message,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleSignUp = async () => {
    setIsLoading(true);
    const provider = new GoogleAuthProvider();
    try {
        const result = await signInWithPopup(auth, provider);
        const user = result.user;
        if (user) {
            await initializeUserProfile(user.uid, {
                email: user.email!,
                name: user.displayName,
                avatarUrl: user.photoURL
            });
            const token = await user.getIdToken();
            const response = await fetch('/api/login', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ token }),
            });

            if (response.ok) {
                toast({ title: "Signed up with Google", description: "Welcome!" });
                window.location.assign('/dashboard');
            } else {
                const errorData = await response.json();
                throw new Error(errorData.error || "API login failed.");
            }
        }
    } catch (error: any) {
        toast({
            variant: "destructive",
            title: "Google Sign-Up Failed",
            description: error.message,
        });
    } finally {
        setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <div className="space-y-2">
            <Label htmlFor="name-signup">Full Name</Label>
            <Input id="name-signup" type="text" placeholder="John Doe" {...register("name")} className="border-2 border-black"/>
            {errors.name && <p className="text-sm text-destructive">{errors.name.message}</p>}
        </div>
        <div className="space-y-2">
          <Label htmlFor="email-signup">Email</Label>
          <Input id="email-signup" type="email" placeholder="<EMAIL>" {...register("email")} className="border-2 border-black"/>
          {errors.email && <p className="text-sm text-destructive">{errors.email.message}</p>}
        </div>
        <div className="space-y-2">
          <Label htmlFor="password-signup">Password</Label>
          <Input id="password-signup" type="password" {...register("password")} className="border-2 border-black"/>
          {errors.password && <p className="text-sm text-destructive">{errors.password.message}</p>}
        </div>
         <div className="space-y-2">
          <Label htmlFor="confirmPassword">Confirm Password</Label>
          <Input id="confirmPassword" type="password" {...register("confirmPassword")} className="border-2 border-black"/>
          {errors.confirmPassword && <p className="text-sm text-destructive">{errors.confirmPassword.message}</p>}
        </div>
        <Button type="submit" className="w-full bg-accent text-accent-foreground border-2 border-black shadow-[2px_2px_0px_#000] hover:shadow-[4px_4px_0px_#000] transition-shadow" disabled={isLoading}>
          {isLoading ? <Loader2 className="animate-spin" /> : <UserPlus className="mr-2" />}
          Create Account
        </Button>
      </form>
       <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <span className="w-full border-t-2 border-black" />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-card px-2 text-muted-foreground">Or sign up with</span>
        </div>
      </div>
      <Button variant="outline" className="w-full h-12 border-2 border-black shadow-[2px_2px_0px_#000]" onClick={handleGoogleSignUp} disabled={isLoading}>
        {isLoading ? <Loader2 className="animate-spin" /> : <><GoogleIcon /><span className="ml-2">Google</span></>}
      </Button>
    </div>
  );
}
