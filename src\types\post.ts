
import type { Comment } from './comment';

export interface MediaItem {
  id: string;
  url: string;
  fileName: string;
  fileSize: number;
  originalSize: number;
  mimeType: string;
  type: 'image' | 'video';
  width?: number;
  height?: number;
  // Video-specific properties
  duration?: number;
  frameRate?: number;
  thumbnailUrl?: string;
}

export interface Post {
  id: string; // Firestore document ID
  content: string;
  space: string;
  authorId: string;
  authorName: string;
  authorAvatar: string;
  createdAt: string;
  votes: number;
  commentsCount: number;
  relevanceReasoning?: string;
  // Media attachments
  media?: MediaItem[];
  // User-specific properties added on the server
  userVote?: 'up' | 'down' | null;
  isSaved?: boolean;
  isFollowed?: boolean;

  // Repost fields
  isRepost?: boolean;
  originalPost?: Post; // For reposts, this will contain the original post data
  originalComment?: Comment; // For reposts of comments
}
