
import type { Comment } from './comment';

export interface Post {
  id: string; // Firestore document ID
  content: string;
  space: string;
  authorId: string;
  authorName: string;
  authorAvatar: string;
  createdAt: string;
  votes: number;
  commentsCount: number;
  relevanceReasoning?: string;
  // User-specific properties added on the server
  userVote?: 'up' | 'down' | null;
  isSaved?: boolean;
  isFollowed?: boolean;

  // Repost fields
  isRepost?: boolean;
  originalPost?: Post; // For reposts, this will contain the original post data
  originalComment?: Comment; // For reposts of comments
}
