
import AppSidebar from "@/components/app-sidebar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Menu, Plus, LayoutGrid } from "lucide-react";
import { Sheet, SheetContent, SheetTrigger, SheetTitle } from "@/components/ui/sheet";
import PageLayout from "@/components/page-layout";
import Link from "next/link";
import { getPublicSpaces } from "@/lib/firestore";
import DiscoverSpacesClient from "@/components/discover-spaces-client";

function AppHeader() {
  return (
    <header className="flex h-16 items-center gap-4 border-b-2 border-black bg-background px-6">
        <Sheet>
            <SheetTrigger asChild>
              <Button
                variant="outline"
                size="icon"
                className="shrink-0 md:hidden border-2 border-black"
              >
                <Menu className="h-5 w-5" />
                <span className="sr-only">Toggle navigation menu</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="p-0 border-r-2 border-black bg-primary">
                <SheetTitle className="sr-only">Navigation Menu</SheetTitle>
                <AppSidebar />
            </SheetContent>
        </Sheet>
        <div className="flex-1 flex justify-between items-center">
            <div className="text-2xl font-bold font-headline flex items-center gap-2">
                <LayoutGrid />
                Discover Spaces
            </div>
            <Button asChild className="bg-accent text-accent-foreground border-2 border-black shadow-[2px_2px_0px_#000]">
              <Link href="/spaces/create">
                  <Plus className="mr-2 h-4 w-4" />
                  Create New Space
              </Link>
            </Button>
        </div>
    </header>
  );
}

export default async function DiscoverSpacesPage() {
    const spaces = await getPublicSpaces();
    return (
        <PageLayout>
            <AppHeader />
            <main className="flex flex-1 flex-col p-6 bg-background">
                <DiscoverSpacesClient spaces={spaces} />
            </main>
        </PageLayout>
    );
}
