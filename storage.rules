rules_version = '2';

// Firebase Storage Security Rules for POLITICA
service firebase.storage {
  match /b/{bucket}/o {
    // Allow authenticated users to upload images to posts/images/
    match /posts/images/{imageId} {
      // Allow read access to all users (public posts)
      allow read: if true;

      // Allow write access only to authenticated users
      // More permissive for development - you can tighten this later
      allow write: if request.auth != null
        && request.auth.uid != null
        // Validate file size (max 10MB)
        && request.resource.size <= 10 * 1024 * 1024
        // Validate file type (images only)
        && request.resource.contentType.matches('image/.*');
        // Removed strict filename validation for development

      // Allow delete only by authenticated users
      allow delete: if request.auth != null
        && request.auth.uid != null;
    }

    // Allow authenticated users to upload videos to posts/videos/
    match /posts/videos/{videoId} {
      // Allow read access to all users (public posts)
      allow read: if true;

      // Allow write access only to authenticated users
      allow write: if request.auth != null
        && request.auth.uid != null
        // Validate file size (max 100MB for videos)
        && request.resource.size <= 100 * 1024 * 1024
        // Validate file type (videos only)
        && request.resource.contentType.matches('video/.*');

      // Allow delete only by authenticated users
      allow delete: if request.auth != null
        && request.auth.uid != null;
    }

    // Allow authenticated users to upload video thumbnails to posts/thumbnails/
    match /posts/thumbnails/{thumbnailId} {
      // Allow read access to all users (public thumbnails)
      allow read: if true;

      // Allow write access only to authenticated users
      allow write: if request.auth != null
        && request.auth.uid != null
        // Validate file size (max 5MB for thumbnails)
        && request.resource.size <= 5 * 1024 * 1024
        // Validate file type (images only for thumbnails)
        && request.resource.contentType.matches('image/.*');

      // Allow delete only by authenticated users
      allow delete: if request.auth != null
        && request.auth.uid != null;
    }
    
    // Allow authenticated users to upload profile images
    match /users/{userId}/profile/{imageId} {
      // Allow read access to all users
      allow read: if true;
      
      // Allow write access only to the user themselves
      allow write: if request.auth != null
        && request.auth.uid == userId
        // Validate file size (max 5MB for profile images)
        && request.resource.size <= 5 * 1024 * 1024
        // Validate file type (images only)
        && request.resource.contentType.matches('image/.*')
        // Validate filename format
        && imageId.matches('^[a-zA-Z0-9_-]+\\.(jpg|jpeg|png|gif|webp)$');
      
      // Allow delete only by the user themselves
      allow delete: if request.auth != null
        && request.auth.uid == userId;
    }

    // Allow test files for CORS testing (development only)
    match /test/{testId} {
      // Allow read/write for authenticated users for testing
      allow read, write: if request.auth != null
        && request.auth.uid != null
        // Small files only for testing
        && request.resource.size <= 1024; // 1KB max for test files
    }

    // Deny all other access
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
