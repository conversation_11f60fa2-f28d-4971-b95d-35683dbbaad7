rules_version = '2';

// Firebase Storage Security Rules for POLITICA
service firebase.storage {
  match /b/{bucket}/o {
    // Allow authenticated users to upload images to posts/images/
    match /posts/images/{imageId} {
      // Allow read access to all users (public posts)
      allow read: if true;
      
      // Allow write access only to authenticated users
      allow write: if request.auth != null
        && request.auth.uid != null
        // Validate file size (max 10MB)
        && request.resource.size <= 10 * 1024 * 1024
        // Validate file type (images only)
        && request.resource.contentType.matches('image/.*')
        // Validate filename format (prevent path traversal)
        && imageId.matches('^[a-zA-Z0-9_-]+\\.(jpg|jpeg|png|gif|webp)$');
      
      // Allow delete only by the file owner (if we track ownership)
      allow delete: if request.auth != null
        && request.auth.uid != null;
    }
    
    // Allow authenticated users to upload profile images
    match /users/{userId}/profile/{imageId} {
      // Allow read access to all users
      allow read: if true;
      
      // Allow write access only to the user themselves
      allow write: if request.auth != null
        && request.auth.uid == userId
        // Validate file size (max 5MB for profile images)
        && request.resource.size <= 5 * 1024 * 1024
        // Validate file type (images only)
        && request.resource.contentType.matches('image/.*')
        // Validate filename format
        && imageId.matches('^[a-zA-Z0-9_-]+\\.(jpg|jpeg|png|gif|webp)$');
      
      // Allow delete only by the user themselves
      allow delete: if request.auth != null
        && request.auth.uid == userId;
    }
    
    // Deny all other access
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
