
'use client';

import { useEffect, useState } from 'react';
import { listenToSpaces } from '@/lib/firestore-client';
import type { Space } from '@/types/space';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { LayoutGrid, Users } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from './ui/avatar';
import Link from 'next/link';
import { Skeleton } from './ui/skeleton';
import { getFirestore } from 'firebase/firestore';
import firebaseApp from '@/lib/firebase';

function DiscoverSpacesSkeleton() {
    return (
        <Card className="border-2 border-black shadow-[4px_4px_0px_black] rounded-lg">
            <CardHeader>
                <CardTitle className="font-bold flex items-center gap-2">
                    <LayoutGrid className="h-5 w-5"/>
                    <span>Discover Spaces</span>
                </CardTitle>
                <CardDescription>
                    Explore new and trending collaboration hubs.
                </CardDescription>
            </CardHeader>
            <CardContent className="grid gap-4">
                {[...Array(3)].map((_, i) => (
                     <div key={i} className="flex items-center gap-4">
                        <Skeleton className="h-10 w-10 rounded-lg border-2 border-black" />
                        <div className="flex-1 space-y-2">
                            <Skeleton className="h-4 w-3/4" />
                            <Skeleton className="h-3 w-1/2" />
                        </div>
                    </div>
                ))}
            </CardContent>
        </Card>
    );
}


export default function DiscoverSpacesWidget() {
  const [spaces, setSpaces] = useState<Space[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const db = getFirestore(firebaseApp);
    const unsubscribe = listenToSpaces(db, (newSpaces) => {
      setSpaces(newSpaces);
      setIsLoading(false);
    }, 5);

    return () => unsubscribe();
  }, []);

  if (isLoading) {
    return <DiscoverSpacesSkeleton />;
  }

  return (
    <Card className="border-2 border-black shadow-[4px_4px_0px_black] rounded-lg">
      <CardHeader>
        <CardTitle className="font-bold flex items-center gap-2">
            <LayoutGrid className="h-5 w-5"/>
            <span>Discover Spaces</span>
        </CardTitle>
        <CardDescription>
            Explore new and trending collaboration hubs.
        </CardDescription>
      </CardHeader>
      <CardContent className="grid gap-4">
        {spaces.length > 0 ? (
            spaces.map((space) => (
                <div key={space.id} className="flex items-center gap-4 p-2 rounded-lg hover:bg-muted">
                    <Avatar className="h-10 w-10 rounded-lg border-2 border-black">
                        <AvatarImage src={space.creatorAvatar} data-ai-hint="abstract geometric" />
                        <AvatarFallback>{space.name.substring(0,2)}</AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                        <Link href={`/spaces/${space.id}`} className="font-semibold hover:underline">{space.name}</Link>
                        <p className="text-xs text-muted-foreground flex items-center gap-1">
                           <Users className="h-3 w-3" /> {space.memberCount} Members
                        </p>
                    </div>
                    <Button asChild variant="secondary" size="sm" className="border-2 border-black shadow-[2px_2px_0px_black]">
                        <Link href={`/spaces/${space.id}`}>View</Link>
                    </Button>
                </div>
            ))
        ) : (
            <p className="text-sm text-muted-foreground text-center py-4">No public spaces available yet.</p>
        )}
        <Button asChild variant="outline" className="w-full border-2 border-black">
            <Link href="/spaces/discover">View All Spaces</Link>
        </Button>
      </CardContent>
    </Card>
  );
}
