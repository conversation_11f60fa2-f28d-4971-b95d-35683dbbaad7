
'use client';

import { useFormStatus } from 'react-dom';
import { useActionState, useEffect, useRef } from 'react';
import { addCommentAction } from '@/app/actions';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Loader2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

const initialState = {
  message: '',
  errors: null,
};

function SubmitButton() {
  const { pending } = useFormStatus();
  return (
    <Button type="submit" disabled={pending} className="bg-accent text-accent-foreground border-2 border-black shadow-[2px_2px_0px_#000] rounded-full font-bold px-6">
      {pending ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
      {pending ? 'Replying...' : 'Reply'}
    </Button>
  );
}

export default function AddCommentForm({ postId, parentCommentId, onCommentAdded }: { postId: string, parentCommentId?: string | null, onCommentAdded?: () => void }) {
  const [state, formAction] = useActionState(addCommentAction, initialState);
  const { toast } = useToast();
  const formRef = useRef<HTMLFormElement>(null);

  useEffect(() => {
    if (state.message === 'success') {
      toast({ title: 'Reply posted!' });
      formRef.current?.reset();
      if (onCommentAdded) {
        onCommentAdded();
      }
    } else if (state.message) {
      toast({ variant: 'destructive', title: 'Error', description: state.message });
    }
  }, [state, toast, onCommentAdded]);

  return (
    <form ref={formRef} action={formAction} className="space-y-4">
      <input type="hidden" name="postId" value={postId} />
      {parentCommentId && <input type="hidden" name="parentCommentId" value={parentCommentId} />}
      <Textarea
        name="content"
        placeholder="Post your reply"
        className="min-h-[20px] border-0 focus-visible:ring-0 focus-visible:ring-offset-0 bg-transparent px-0 text-lg"
        required
      />
      {state.errors?.content && (
        <p className="text-sm font-medium text-destructive">{state.errors.content[0]}</p>
      )}
      <div className="flex justify-end">
        <SubmitButton />
      </div>
    </form>
  );
}
