
import { getComment, getComments, getUserProfile } from "@/lib/firestore";
import { getSession } from "@/lib/session";
import PageLayout from "@/components/page-layout";
import { Button } from "@/components/ui/button";
import AppSidebar from "@/components/app-sidebar";
import { 
    Menu, 
    ArrowLeft,
} from "lucide-react";
import { Sheet, Sheet<PERSON>ontent, SheetTrigger, SheetTitle } from "@/components/ui/sheet";
import { notFound } from "next/navigation";
import Link from "next/link";
import type { UserProfile } from "@/types/user";
import MainCommentThread from "@/components/main-comment-thread";

export default async function CommentPage({ params }: { params: { postId: string, commentId: string } }) {
  const session = await getSession();
  const [mainComment, allComments, currentUserProfile] = await Promise.all([
    getComment(params.postId, params.commentId, session?.uid),
    getComments(params.postId, session?.uid),
    session ? getUserProfile(session.uid) : Promise.resolve(null as UserProfile | null)
  ]);

  if (!mainComment) {
    notFound();
  }

  return (
    <PageLayout>
      <header className="flex h-16 shrink-0 items-center gap-4 border-b-2 border-black bg-background px-4 md:px-6 sticky top-0 z-10">
        <Sheet>
            <SheetTrigger asChild>
              <Button
                variant="outline"
                size="icon"
                className="shrink-0 md:hidden border-2 border-black"
              >
                <Menu className="h-5 w-5" />
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="p-0 border-r-2 border-black bg-primary">
                <SheetTitle className="sr-only">Navigation Menu</SheetTitle>
                <AppSidebar />
            </SheetContent>
        </Sheet>
        <Button variant="ghost" size="icon" asChild>
          <Link href={`/post/${params.postId}`} aria-label="Back to post">
            <ArrowLeft />
          </Link>
        </Button>
        <h1 className="text-xl font-bold font-headline">Thread</h1>
      </header>
      <MainCommentThread
        postId={params.postId}
        commentId={params.commentId}
        initialMainComment={mainComment}
        allComments={allComments}
        currentUserProfile={currentUserProfile}
        currentUserId={session?.uid || null}
      />
    </PageLayout>
  );
}
