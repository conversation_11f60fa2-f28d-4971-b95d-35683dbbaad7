
'use client';

import { useState, useEffect } from 'react';
import type { UserProfile } from "@/types/user";
import type { Comment as CommentType } from "@/types/comment";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { format } from 'date-fns';
import Link from "next/link";
import CommentActions from "@/components/comment-actions";
import AddCommentForm from "@/components/add-comment-form";
import Comment from "@/components/comment";
import { Separator } from './ui/separator';

// Helper function to build the thread from a flat list of comments
const buildCommentTree = (comments: CommentType[], parentId: string | null): CommentType[] => {
    const tree: CommentType[] = [];
    const childrenOf = new Map<string, CommentType[]>();

    comments.forEach(comment => {
        const pId = comment.parentCommentId || null;
        if (!childrenOf.has(pId)) {
            childrenOf.set(pId, []);
        }
        childrenOf.get(pId)!.push(comment);
    });

    const getChildren = (id: string | null): CommentType[] => {
        const children = childrenOf.get(id) || [];
        children.forEach(child => {
            child.replies = getChildren(child.id);
        });
        return children.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
    };

    return getChildren(parentId);
};


interface MainCommentThreadProps {
    postId: string;
    commentId: string;
    initialMainComment: CommentType;
    allComments: CommentType[];
    currentUserProfile: UserProfile | null;
    currentUserId: string | null;
}

export default function MainCommentThread({
    postId,
    commentId,
    initialMainComment,
    allComments,
    currentUserProfile,
    currentUserId,
}: MainCommentThreadProps) {
    const [isReplying, setIsReplying] = useState(false);
    const [isMounted, setIsMounted] = useState(false);

    useEffect(() => {
        setIsMounted(true);
    }, []);
    
    const mainComment = {
        ...initialMainComment,
        replies: buildCommentTree(allComments, initialMainComment.id)
    };

    const handleReplyClick = () => {
        if (!currentUserProfile) return;
        setIsReplying(prev => !prev);
    };

    return (
        <main className="flex-1 bg-background">
            <div className="p-4 md:p-6">
                <div className="flex flex-col border-2 border-black rounded-lg shadow-[4px_4px_0px_black] p-4">
                    <div className="flex items-center gap-3">
                        <Avatar className="h-12 w-12 border-2 border-black">
                            <AvatarImage src={mainComment.authorAvatar} alt={mainComment.authorName}/>
                            <AvatarFallback>{mainComment.authorName.substring(0, 2).toUpperCase()}</AvatarFallback>
                        </Avatar>
                        <div>
                            <p className="font-bold">{mainComment.authorName}</p>
                            <p className="text-sm text-muted-foreground">@{mainComment.authorName.toLowerCase().replace(/\s/g, '')}</p>
                        </div>
                    </div>

                    <div className="py-6 space-y-4">
                        <p className="text-xl whitespace-pre-wrap leading-relaxed">{mainComment.content}</p>
                        <p className="text-sm text-muted-foreground">Replying to <Link href={`/post/${postId}`} className="text-accent hover:underline">the original post</Link></p>
                        <p className="text-sm text-muted-foreground">
                            {isMounted ? format(new Date(mainComment.createdAt), "h:mm a · MMM d, yyyy") : null}
                        </p>
                    </div>

                    <Separator />

                    <div className="flex gap-4 py-2 text-sm text-muted-foreground">
                        {mainComment.votes > 0 && <span className="font-bold text-foreground">{mainComment.votes} <span className="font-normal text-muted-foreground">Likes</span></span>}
                        {mainComment.commentsCount > 0 && <span className="font-bold text-foreground">{mainComment.commentsCount} <span className="font-normal text-muted-foreground">Replies</span></span>}
                    </div>
                    
                    <div className="pt-2">
                        <CommentActions comment={mainComment} currentUserId={currentUserId} onReplyClick={handleReplyClick}/>
                    </div>
                </div>

                <div className="pt-8">
                    {isReplying && currentUserProfile && (
                        <div className="flex gap-4 py-4 border-t-2 border-black mb-6">
                            <Avatar className="h-10 w-10 border-2 border-black">
                                <AvatarImage src={currentUserProfile.avatarUrl} alt={currentUserProfile.name}/>
                                <AvatarFallback>{currentUserProfile.name.substring(0, 2).toUpperCase()}</AvatarFallback>
                            </Avatar>
                            <div className="flex-1">
                                <AddCommentForm postId={postId} parentCommentId={commentId} onCommentAdded={() => setIsReplying(false)} />
                            </div>
                        </div>
                    )}
                    
                    {!isReplying && (
                         <div className="py-4 text-muted-foreground">
                            {!currentUserProfile ? (
                                <p>
                                    <Link href="/" className="text-accent hover:underline">Log in</Link> to post a reply.
                                </p>
                            ) : (
                                 <button onClick={handleReplyClick} className="text-muted-foreground hover:text-accent">Post your reply</button>
                            )}
                        </div>
                    )}
                    
                    {mainComment.replies && mainComment.replies.length > 0 && (
                        <div className="space-y-8 pt-8 border-t-2 border-black">
                            {mainComment.replies.map(reply => (
                                <Comment 
                                    key={reply.id} 
                                    comment={reply} 
                                    currentUserId={currentUserId}
                                    currentUserProfile={currentUserProfile}
                                />
                            ))}
                        </div>
                    )}
                </div>
            </div>
        </main>
    );
}
