'use client';

import { useState, useCallback, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import MediaUpload from './media-upload';
import VideoUpload from './video-upload';
import PostMedia from './post-media';
import CorsTest from './cors-test';
import type { MediaItem } from '@/types/post';
import { formatFileSize } from '@/lib/image-compression';
import { formatVideoSize, formatVideoDuration } from '@/lib/video-processing';

export default function MediaUploadDemoEnhanced() {
  const [images, setImages] = useState<MediaItem[]>([]);
  const [video, setVideo] = useState<MediaItem | null>(null);
  const [allMedia, setAllMedia] = useState<MediaItem[]>([]);

  // Update combined media whenever images or video changes
  useEffect(() => {
    const combined: MediaItem[] = [];
    if (video) combined.push(video);
    combined.push(...images);
    setAllMedia(combined);
  }, [images, video]);

  // Stable callback functions using useCallback
  const handleImagesChange = useCallback((newImages: MediaItem[]) => {
    setImages(newImages);
  }, []);

  const handleVideoChange = useCallback((newVideo: MediaItem | null) => {
    setVideo(newVideo);
  }, []);

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card className="border-2 border-black">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            🎬 Enhanced Media Upload Demo
            <Badge variant="secondary">Images + Videos</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* CORS Test Section */}
          <CorsTest />

          {/* Upload Tabs */}
          <Tabs defaultValue="combined" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="combined">Combined Upload</TabsTrigger>
              <TabsTrigger value="images">Images Only</TabsTrigger>
              <TabsTrigger value="video">Video Only</TabsTrigger>
              <TabsTrigger value="preview">Preview</TabsTrigger>
            </TabsList>

            <TabsContent value="combined" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                {/* Images Upload */}
                <div>
                  <h3 className="text-lg font-semibold mb-3">Upload Images</h3>
                  <MediaUpload 
                    onMediaChange={handleImagesChange}
                    maxFiles={4}
                    disabled={false}
                    allowImages={true}
                    allowVideos={false}
                  />
                </div>

                {/* Video Upload */}
                <div>
                  <h3 className="text-lg font-semibold mb-3">Upload Video</h3>
                  <VideoUpload 
                    onVideoChange={handleVideoChange}
                    disabled={false}
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="images" className="space-y-4">
              <div>
                <h3 className="text-lg font-semibold mb-3">Upload Images</h3>
                <MediaUpload 
                  onMediaChange={handleImagesChange}
                  maxFiles={6}
                  disabled={false}
                  allowImages={true}
                  allowVideos={false}
                />
              </div>
            </TabsContent>

            <TabsContent value="video" className="space-y-4">
              <div>
                <h3 className="text-lg font-semibold mb-3">Upload Video</h3>
                <VideoUpload 
                  onVideoChange={handleVideoChange}
                  disabled={false}
                />
              </div>
            </TabsContent>

            <TabsContent value="preview" className="space-y-4">
              {/* Media Preview Section */}
              {allMedia.length > 0 ? (
                <div>
                  <h3 className="text-lg font-semibold mb-3">Media Preview</h3>
                  <PostMedia media={allMedia} />
                  
                  {/* Media Statistics */}
                  <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                    <h4 className="font-medium mb-2">Upload Statistics</h4>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Total Files:</span>
                        <div className="font-medium">{allMedia.length}</div>
                      </div>
                      <div>
                        <span className="text-gray-600">Images:</span>
                        <div className="font-medium">{allMedia.filter(m => m.type === 'image').length}</div>
                      </div>
                      <div>
                        <span className="text-gray-600">Videos:</span>
                        <div className="font-medium">{allMedia.filter(m => m.type === 'video').length}</div>
                      </div>
                      <div>
                        <span className="text-gray-600">Total Size:</span>
                        <div className="font-medium">
                          {formatFileSize(allMedia.reduce((sum, m) => sum + m.fileSize, 0))}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Detailed Media List */}
                  <div className="mt-4 space-y-2">
                    <h4 className="font-medium">Uploaded Files</h4>
                    {allMedia.map((item, index) => (
                      <div key={item.id} className="flex items-center justify-between p-3 bg-white border rounded-lg">
                        <div className="flex items-center gap-3">
                          <Badge variant={item.type === 'video' ? 'default' : 'secondary'}>
                            {item.type === 'video' ? '🎥' : '🖼️'} {item.type}
                          </Badge>
                          <div>
                            <div className="font-medium">{item.fileName}</div>
                            <div className="text-sm text-gray-600">
                              {item.type === 'video' && item.duration ? (
                                <>
                                  {formatVideoDuration(item.duration)} • {item.width}×{item.height} • {formatVideoSize(item.fileSize)}
                                </>
                              ) : (
                                <>
                                  {item.width}×{item.height} • {formatFileSize(item.fileSize)}
                                </>
                              )}
                            </div>
                          </div>
                        </div>
                        <div className="text-sm text-gray-500">
                          Compression: {((item.originalSize - item.fileSize) / item.originalSize * 100).toFixed(1)}% saved
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <p>No media uploaded yet</p>
                  <p className="text-sm">Upload some images or videos to see the preview</p>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Technical Information */}
      <Card className="border-2 border-black">
        <CardHeader>
          <CardTitle>🔧 Technical Features</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold mb-2">Image Features</h4>
              <ul className="text-sm space-y-1 text-gray-600">
                <li>• Client-side compression with quality control</li>
                <li>• Drag & drop interface</li>
                <li>• Real-time progress indicators</li>
                <li>• Multiple format support (PNG, JPG, WebP, GIF)</li>
                <li>• Automatic resizing and optimization</li>
                <li>• Preview with remove functionality</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-2">Video Features</h4>
              <ul className="text-sm space-y-1 text-gray-600">
                <li>• Video metadata extraction</li>
                <li>• Automatic thumbnail generation</li>
                <li>• Duration and size validation</li>
                <li>• Multiple format support (MP4, WebM, MOV)</li>
                <li>• Video player with controls</li>
                <li>• Compression progress tracking</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
