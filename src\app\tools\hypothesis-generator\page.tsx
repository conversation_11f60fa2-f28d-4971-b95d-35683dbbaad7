
import { <PERSON><PERSON> } from "@/components/ui/button";
import AppSidebar from "@/components/app-sidebar";
import { Menu } from "lucide-react";
import { Sheet, SheetTrigger, SheetContent, SheetTitle } from "@/components/ui/sheet";
import HypothesisGeneratorClient from "@/components/tools/hypothesis-generator-client";
import PageLayout from "@/components/page-layout";

function AppHeader() {
  return (
    <header className="flex h-16 items-center gap-4 border-b-2 border-black bg-background px-6">
       <Sheet>
            <SheetTrigger asChild>
              <Button
                variant="outline"
                size="icon"
                className="shrink-0 md:hidden border-2 border-black"
              >
                <Menu className="h-5 w-5" />
                <span className="sr-only">Toggle navigation menu</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="p-0 border-r-2 border-black bg-primary">
                <SheetTitle className="sr-only">Navigation Menu</SheetTitle>
                <AppSidebar />
            </SheetContent>
        </Sheet>
        <div className="text-2xl font-bold font-headline">Hypothesis Generator</div>
    </header>
  );
}

export default function HypothesisGeneratorPage() {
  return (
    <PageLayout>
      <AppHeader />
      <main className="flex flex-1 flex-col p-6 bg-background">
          <HypothesisGeneratorClient />
      </main>
    </PageLayout>
  );
}
