
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { jwtVerify } from 'jose';

// This is the minimum required to verify the session cookie.
// It does not check for revocation, which is fine for middleware.
// The full check (including revocation) is done in `getSession` for Server Actions/Components.
async function verifySession(sessionCookie: string): Promise<boolean> {
  try {
    const response = await fetch('https://www.googleapis.com/robot/v1/metadata/x509/<EMAIL>');
    const publicKeys = await response.json();
    
    // The `jwtVerify` function from `jose` is runtime-agnostic and works in the middleware.
    await jwtVerify(sessionCookie, async (header) => {
      const x509 = publicKeys[header.kid as string];
      return x509;
    });

    return true;
  } catch (error) {
    // console.error("Middleware session verification failed:", error);
    return false;
  }
}


export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const sessionCookie = request.cookies.get('session')?.value;
  
  const isAuthPage = pathname === '/';
  
  let hasValidSession = false;
  if (sessionCookie) {
    hasValidSession = await verifySession(sessionCookie);
  }

  if (isAuthPage) {
    if (hasValidSession) {
      // If a logged-in user tries to access the login page, redirect them to the dashboard.
      return NextResponse.redirect(new URL('/dashboard', request.url));
    }
    // Allow unauthenticated users to access the login page.
    return NextResponse.next();
  }

  // For all other protected routes, a session is required.
  if (!hasValidSession) {
    // If no session exists, redirect to the login page.
    const response = NextResponse.redirect(new URL('/', request.url));
    
    // It's good practice to clear a potentially invalid cookie if the session verification failed.
    if (request.cookies.has('session')) {
      response.cookies.delete('session');
    }
    return response;
  }

  // If a valid session exists, allow the request to proceed.
  return NextResponse.next();
}

export const config = {
  // Match all routes except for static files, api routes, and image assets
  matcher: ['/((?!api|_next/static|_next/image|favicon.ico).*)'],
};
