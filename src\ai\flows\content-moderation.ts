// src/ai/flows/content-moderation.ts
'use server';

/**
 * @fileOverview Content moderation flow to detect toxic, biased, or inappropriate content.
 *
 * - moderateContent - A function that moderates the input content.
 * - ModerateContentInput - The input type for the moderateContent function.
 * - ModerateContentOutput - The return type for the moderateContent function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const ModerateContentInputSchema = z.object({
  content: z.string().describe('The content to be moderated.'),
});
export type ModerateContentInput = z.infer<typeof ModerateContentInputSchema>;

const ModerateContentOutputSchema = z.object({
  isToxic: z.boolean().describe('Whether the content is toxic.'),
  toxicityScore: z.number().describe('The toxicity score of the content (0-1).'),
  isBiased: z.boolean().describe('Whether the content is biased.'),
  biasType: z.string().optional().describe('The type of bias detected, if any.'),
  isInappropriate: z.boolean().describe('Whether the content is inappropriate.'),
  inappropriateReason: z
    .string()
    .optional()
    .describe('The reason why the content is inappropriate, if any.'),
  flaggedCategories: z
    .array(z.string())
    .describe('List of content categories that triggered moderation.'),
  summary: z.string().describe('A summary of the moderation analysis.'),
});
export type ModerateContentOutput = z.infer<typeof ModerateContentOutputSchema>;

export async function moderateContent(input: ModerateContentInput): Promise<ModerateContentOutput> {
  return moderateContentFlow(input);
}

const moderateContentPrompt = ai.definePrompt({
  name: 'moderateContentPrompt',
  input: {schema: ModerateContentInputSchema},
  output: {schema: ModerateContentOutputSchema},
  prompt: `You are a content moderation AI that analyzes text content and identifies toxicity, bias, and inappropriate elements.

  Analyze the following content:
  {{content}}

  Provide a detailed analysis based on the following criteria:

  - **Toxicity:** Identify any toxic language, hate speech, or personal attacks. Provide a toxicity score (0-1).
  - **Bias:** Detect any biased statements or discriminatory language. If bias is detected, specify the type of bias.
  - **Inappropriateness:** Determine if the content is inappropriate based on community guidelines.
  - **Flagged Categories:** List content categories that trigger moderation (e.g., hate_speech, violence, self_harm, sexual_content).

  Based on the analysis, set the appropriate boolean flags (isToxic, isBiased, isInappropriate).
  Provide a summary of your analysis.

  Ensure that the output is valid JSON and conforms to the ModerateContentOutputSchema.
  `,
  config: {
    safetySettings: [
      {
        category: 'HARM_CATEGORY_HATE_SPEECH',
        threshold: 'BLOCK_ONLY_HIGH',
      },
      {
        category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
        threshold: 'BLOCK_NONE',
      },
      {
        category: 'HARM_CATEGORY_HARASSMENT',
        threshold: 'BLOCK_MEDIUM_AND_ABOVE',
      },
      {
        category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
        threshold: 'BLOCK_LOW_AND_ABOVE',
      },
    ],
  },
});

const moderateContentFlow = ai.defineFlow(
  {
    name: 'moderateContentFlow',
    inputSchema: ModerateContentInputSchema,
    outputSchema: ModerateContentOutputSchema,
  },
  async input => {
    const {output} = await moderateContentPrompt(input);
    return output!;
  }
);
