'use server';

/**
 * @fileOverview Provides AI-powered connection recommendations based on user profile, interests, and activity.
 *
 * - aiConnectionSuggestions - A function that returns AI-powered connection suggestions.
 * - AiConnectionSuggestionsInput - The input type for the aiConnectionSuggestions function.
 * - AiConnectionSuggestionsOutput - The return type for the aiConnectionSuggestions function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const AiConnectionSuggestionsInputSchema = z.object({
  userProfile: z.string().describe('The user profile including skills, experience, and interests.'),
  interests: z.string().describe('The user interests.'),
  recentActivity: z.string().describe('The user recent activity on the platform.'),
});
export type AiConnectionSuggestionsInput = z.infer<typeof AiConnectionSuggestionsInputSchema>;

const AiConnectionSuggestionsOutputSchema = z.object({
  suggestedConnections: z.array(
    z.object({
      userId: z.string().describe('The user ID of the suggested connection.'),
      name: z.string().describe('The name of the suggested connection.'),
      reason: z.string().describe('The reason for the suggested connection.'),
      profileSummary: z.string().describe('A short summary of the suggested connection profile.'),
    })
  ).describe('A list of suggested connections.'),
});
export type AiConnectionSuggestionsOutput = z.infer<typeof AiConnectionSuggestionsOutputSchema>;

export async function aiConnectionSuggestions(input: AiConnectionSuggestionsInput): Promise<AiConnectionSuggestionsOutput> {
  return aiConnectionSuggestionsFlow(input);
}

const prompt = ai.definePrompt({
  name: 'aiConnectionSuggestionsPrompt',
  input: {schema: AiConnectionSuggestionsInputSchema},
  output: {schema: AiConnectionSuggestionsOutputSchema},
  prompt: `You are an AI assistant designed to provide connection suggestions for users on a professional networking platform.

  Given the following information about the user, provide a list of suggested connections. For each suggestion, include a unique user ID, name, a brief profile summary, and a clear reason for the suggestion.

  User Profile: {{{userProfile}}}
  User Interests: {{{interests}}}
  Recent Activity: {{{recentActivity}}}

  Your response must be a JSON object with a "suggestedConnections" key, which holds an array of the connection suggestion objects.
  `,
});

const aiConnectionSuggestionsFlow = ai.defineFlow(
  {
    name: 'aiConnectionSuggestionsFlow',
    inputSchema: AiConnectionSuggestionsInputSchema,
    outputSchema: AiConnectionSuggestionsOutputSchema,
  },
  async input => {
    const {output} = await prompt(input);
    return output!;
  }
);
