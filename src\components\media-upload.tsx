'use client';

import { useState, useCallback, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Upload,
  X,
  Image as ImageIcon,
  Loader2,
  CheckCircle,
  AlertCircle,
  FileImage,
  Video,
  Play
} from 'lucide-react';
import { cn } from '@/lib/utils';
import {
  compressImage,
  isValidImageFile,
  formatFileSize,
  type CompressionResult,
  type CompressionProgress
} from '@/lib/image-compression';
import {
  isValidVideoFile,
  compressVideo,
  type VideoCompressionResult,
  type VideoCompressionProgress
} from '@/lib/video-processing';
import {
  uploadImage,
  uploadVideo,
  uploadResultToMediaItem,
  type UploadProgress
} from '@/lib/firebase-storage';
import type { MediaItem } from '@/types/post';

interface MediaUploadProps {
  onMediaChange: (media: MediaItem[]) => void;
  maxFiles?: number;
  disabled?: boolean;
  className?: string;
  allowVideos?: boolean;
  allowImages?: boolean;
}

interface FileState {
  id: string;
  file: File;
  preview: string;
  status: 'pending' | 'compressing' | 'uploading' | 'complete' | 'error';
  compressionProgress: number;
  uploadProgress: number;
  error?: string;
  compressionResult?: CompressionResult;
  videoCompressionResult?: VideoCompressionResult;
  mediaItem?: MediaItem;
  fileType: 'image' | 'video';
}

export default function MediaUpload({
  onMediaChange,
  maxFiles = 4,
  disabled = false,
  className,
  allowVideos = true,
  allowImages = true
}: MediaUploadProps) {
  const [files, setFiles] = useState<FileState[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Effect to notify parent when completed media changes
  useEffect(() => {
    const completedMedia = files
      .filter(file => file.status === 'complete' && file.mediaItem)
      .map(file => file.mediaItem!);
    onMediaChange(completedMedia);
  }, [files, onMediaChange]);

  const updateFileState = useCallback((id: string, updates: Partial<FileState>) => {
    setFiles(prev => prev.map(file => 
      file.id === id ? { ...file, ...updates } : file
    ));
  }, []);

  const removeFile = useCallback((id: string) => {
    setFiles(prev => prev.filter(file => file.id !== id));
  }, []);

  const processFile = useCallback(async (file: File) => {
    const id = Math.random().toString(36).substring(2);
    const preview = URL.createObjectURL(file);
    const isImage = isValidImageFile(file);
    const isVideo = isValidVideoFile(file);

    const fileState: FileState = {
      id,
      file,
      preview,
      status: 'pending',
      compressionProgress: 0,
      uploadProgress: 0,
      fileType: isImage ? 'image' : 'video'
    };

    setFiles(prev => [...prev, fileState]);

    try {
      // Start compression
      updateFileState(id, { status: 'compressing' });

      if (isImage) {
        // Process image
        const compressionResult = await compressImage(
          file,
          { maxSizeMB: 1, maxWidthOrHeight: 1920, quality: 0.8 },
          (progress: CompressionProgress) => {
            updateFileState(id, {
              compressionProgress: progress.progress,
              status: progress.stage === 'error' ? 'error' : 'compressing',
              error: progress.error
            });
          }
        );

        updateFileState(id, {
          compressionResult,
          status: 'uploading',
          compressionProgress: 100
        });

        // Upload image
        const uploadResult = await uploadImage(
          compressionResult.compressedFile,
          compressionResult.originalSize,
          { width: compressionResult.width!, height: compressionResult.height! },
          (progress: UploadProgress) => {
            updateFileState(id, {
              uploadProgress: progress.progress,
              status: progress.stage === 'error' ? 'error' : 'uploading',
              error: progress.error
            });
          }
        );

        const mediaItem = uploadResultToMediaItem(uploadResult, id);

        updateFileState(id, {
          status: 'complete',
          uploadProgress: 100,
          mediaItem
        });

      } else if (isVideo) {
        // Process video
        const videoCompressionResult = await compressVideo(
          file,
          { maxSizeMB: 50, maxWidth: 1280, maxHeight: 720, quality: 'medium' },
          (progress: VideoCompressionProgress) => {
            updateFileState(id, {
              compressionProgress: progress.progress,
              status: progress.stage === 'error' ? 'error' : 'compressing',
              error: progress.error
            });
          }
        );

        updateFileState(id, {
          videoCompressionResult,
          status: 'uploading',
          compressionProgress: 100
        });

        // Upload video
        const uploadResult = await uploadVideo(
          videoCompressionResult.compressedFile,
          videoCompressionResult.originalSize,
          {
            width: videoCompressionResult.width,
            height: videoCompressionResult.height,
            duration: videoCompressionResult.duration,
            frameRate: videoCompressionResult.frameRate
          },
          videoCompressionResult.thumbnail,
          (progress: UploadProgress) => {
            updateFileState(id, {
              uploadProgress: progress.progress,
              status: progress.stage === 'error' ? 'error' : 'uploading',
              error: progress.error
            });
          }
        );

        updateFileState(id, {
          status: 'complete',
          uploadProgress: 100,
          mediaItem: uploadResult.mediaItem!
        });
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Processing failed';
      updateFileState(id, {
        status: 'error',
        error: errorMessage
      });
    }
  }, [updateFileState]);

  const handleFileSelect = useCallback((selectedFiles: FileList | null) => {
    if (!selectedFiles || disabled) return;

    const validFiles = Array.from(selectedFiles).filter(file => {
      const isImage = isValidImageFile(file);
      const isVideo = isValidVideoFile(file);

      if (!allowImages && isImage) {
        console.warn(`${file.name} - Images not allowed`);
        return false;
      }

      if (!allowVideos && isVideo) {
        console.warn(`${file.name} - Videos not allowed`);
        return false;
      }

      if (!isImage && !isVideo) {
        console.warn(`${file.name} is not a supported format`);
        return false;
      }

      const maxSize = isVideo ? 100 * 1024 * 1024 : 10 * 1024 * 1024; // 100MB for videos, 10MB for images
      if (file.size > maxSize) {
        console.warn(`${file.name} is too large (max ${isVideo ? '100MB' : '10MB'})`);
        return false;
      }

      return true;
    });

    const remainingSlots = maxFiles - files.length;
    const filesToProcess = validFiles.slice(0, remainingSlots);

    filesToProcess.forEach(processFile);
  }, [disabled, maxFiles, files.length, processFile]);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    handleFileSelect(e.dataTransfer.files);
  }, [handleFileSelect]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const canAddMore = files.length < maxFiles && !disabled;

  return (
    <div className={cn("space-y-4", className)}>
      {/* Upload Area */}
      {canAddMore && (
        <div
          className={cn(
            "border-2 border-dashed border-gray-300 rounded-lg p-6 text-center transition-colors",
            "hover:border-gray-400 hover:bg-gray-50",
            isDragOver && "border-blue-500 bg-blue-50",
            disabled && "opacity-50 cursor-not-allowed"
          )}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
        >
          <input
            ref={fileInputRef}
            type="file"
            multiple
            accept={
              allowImages && allowVideos ? "image/*,video/*" :
              allowVideos ? "video/*" :
              "image/*"
            }
            onChange={(e) => handleFileSelect(e.target.files)}
            className="hidden"
            disabled={disabled}
          />
          
          <div className="flex flex-col items-center space-y-2">
            <Upload className="h-8 w-8 text-gray-400" />
            <div>
              <Button
                type="button"
                variant="outline"
                onClick={() => fileInputRef.current?.click()}
                disabled={disabled}
                className="border-2 border-black"
              >
                <ImageIcon className="mr-2 h-4 w-4" />
                Choose {allowImages && allowVideos ? 'Media' : allowVideos ? 'Videos' : 'Images'}
              </Button>
            </div>
            <p className="text-sm text-gray-500">
              or drag and drop {allowImages && allowVideos ? 'media files' : allowVideos ? 'videos' : 'images'} here
            </p>
            <p className="text-xs text-gray-400">
              {allowImages && allowVideos ? 'Images (PNG, JPG, WebP, GIF up to 10MB) • Videos (MP4, WebM, MOV up to 100MB)' :
               allowVideos ? 'MP4, WebM, MOV up to 100MB' :
               'PNG, JPG, WebP, GIF up to 10MB'} ({files.length}/{maxFiles} files)
            </p>
          </div>
        </div>
      )}

      {/* File List */}
      {files.length > 0 && (
        <div className="space-y-3">
          {files.map((fileState) => (
            <FilePreview
              key={fileState.id}
              fileState={fileState}
              onRemove={() => removeFile(fileState.id)}
            />
          ))}
        </div>
      )}
    </div>
  );
}

interface FilePreviewProps {
  fileState: FileState;
  onRemove: () => void;
}

function FilePreview({ fileState, onRemove }: FilePreviewProps) {
  const { file, preview, status, compressionProgress, uploadProgress, error, compressionResult } = fileState;

  const getStatusIcon = () => {
    switch (status) {
      case 'compressing':
      case 'uploading':
        return <Loader2 className="h-4 w-4 animate-spin" />;
      case 'complete':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <FileImage className="h-4 w-4" />;
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'compressing':
        return `Compressing... ${compressionProgress}%`;
      case 'uploading':
        return `Uploading... ${uploadProgress}%`;
      case 'complete':
        return 'Complete';
      case 'error':
        return error || 'Error';
      default:
        return 'Pending';
    }
  };

  const getProgress = () => {
    if (status === 'compressing') return compressionProgress;
    if (status === 'uploading') return uploadProgress;
    if (status === 'complete') return 100;
    return 0;
  };

  return (
    <Card className="border-2 border-black">
      <CardContent className="p-4">
        <div className="flex items-start space-x-4">
          {/* Media Preview */}
          <div className="flex-shrink-0 relative">
            {fileType === 'video' ? (
              <div className="w-16 h-16 bg-gray-900 rounded border-2 border-black flex items-center justify-center">
                <Video className="h-6 w-6 text-white" />
              </div>
            ) : (
              <img
                src={preview}
                alt={file.name}
                className="w-16 h-16 object-cover rounded border-2 border-black"
              />
            )}
            {fileType === 'video' && (
              <div className="absolute inset-0 flex items-center justify-center">
                <Play className="h-4 w-4 text-white" />
              </div>
            )}
          </div>

          {/* File Info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between">
              <p className="text-sm font-medium truncate">{file.name}</p>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={onRemove}
                className="h-6 w-6 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            <div className="flex items-center space-x-2 mt-1">
              {getStatusIcon()}
              <span className="text-sm text-gray-600">{getStatusText()}</span>
            </div>

            {/* Progress Bar */}
            {(status === 'compressing' || status === 'uploading') && (
              <Progress value={getProgress()} className="mt-2 h-2" />
            )}

            {/* File Size Info */}
            <div className="flex items-center space-x-2 mt-2">
              <Badge variant="outline" className="text-xs">
                {formatFileSize(file.size)}
              </Badge>
              {compressionResult && (
                <>
                  <span className="text-xs text-gray-400">→</span>
                  <Badge variant="outline" className="text-xs">
                    {formatFileSize(compressionResult.compressedSize)}
                  </Badge>
                  <Badge variant="secondary" className="text-xs">
                    -{compressionResult.compressionRatio}%
                  </Badge>
                </>
              )}
            </div>

            {/* Error Message */}
            {status === 'error' && error && (
              <p className="text-xs text-red-500 mt-1">{error}</p>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
