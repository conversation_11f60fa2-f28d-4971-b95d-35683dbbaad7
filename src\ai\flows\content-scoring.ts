'use server';
/**
 * @fileOverview Content scoring AI agent.
 *
 * - scoreContent - A function that scores content based on accuracy, relevance, and credibility.
 * - ContentScoringInput - The input type for the scoreContent function.
 * - ContentScoringOutput - The return type for the scoreContent function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const ContentScoringInputSchema = z.object({
  content: z.string().describe('The content to be scored.'),
});
export type ContentScoringInput = z.infer<typeof ContentScoringInputSchema>;

const ContentScoringOutputSchema = z.object({
  accuracyScore: z.number().describe('A score representing the accuracy of the content (0-1).'),
  relevanceScore: z.number().describe('A score representing the relevance of the content (0-1).'),
  credibilityScore: z.number().describe('A score representing the credibility of the content (0-1).'),
  justification: z.string().describe('A brief justification for the assigned scores.'),
});
export type ContentScoringOutput = z.infer<typeof ContentScoringOutputSchema>;

export async function scoreContent(input: ContentScoringInput): Promise<ContentScoringOutput> {
  return contentScoringFlow(input);
}

const prompt = ai.definePrompt({
  name: 'contentScoringPrompt',
  input: {schema: ContentScoringInputSchema},
  output: {schema: ContentScoringOutputSchema},
  prompt: `You are an AI expert in content scoring. You will evaluate the provided content based on accuracy, relevance, and credibility.

  Provide scores between 0 and 1 for each category.
  Also include a short justification for why you gave the scores you did.

  Content: {{{content}}}
  `,
});

const contentScoringFlow = ai.defineFlow(
  {
    name: 'contentScoringFlow',
    inputSchema: ContentScoringInputSchema,
    outputSchema: ContentScoringOutputSchema,
  },
  async input => {
    const {output} = await prompt(input);
    return output!;
  }
);
