
'use server';

import { cookies } from 'next/headers';
import { auth as adminAuth } from '@/lib/firebase-admin';
import type { DecodedIdToken } from 'firebase-admin/auth';

/**
 * Gets the current user's session from the session cookie.
 * This is the single source of truth for user authentication on the server for Server Actions and Server Components.
 * It verifies the cookie and checks for revocation.
 * @returns {Promise<DecodedIdToken | null>} The decoded user token or null if the user is not authenticated.
 */
export async function getSession(): Promise<DecodedIdToken | null> {
  const cookieStore = await cookies();
  const sessionCookie = cookieStore.get('session')?.value;
  if (!sessionCookie) {
    return null;
  }

  try {
    // The `true` parameter checks if the cookie has been revoked.
    const decodedToken = await adminAuth.verifySessionCookie(sessionCookie, true);
    return decodedToken;
  } catch (error) {
    // Session cookie is invalid, expired, or revoked.
    // It's safe to treat this as an unauthenticated user.
    console.error('Session verification failed in getSession:', error);
    return null;
  }
}
