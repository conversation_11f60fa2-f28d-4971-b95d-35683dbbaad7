
'use client';

import type { Firestore } from "firebase/firestore";
import { 
    collection, 
    query, 
    orderBy, 
    onSnapshot, 
    Timestamp, 
    limit,
    where
} from "firebase/firestore";
import type { Post } from "@/types/post";
import type { Space } from "@/types/space";

/**
 * Listens for real-time updates to the posts collection.
 * Can be filtered by a specific "space".
 */
export function listenToPosts(
  db: Firestore,
  callback: (posts: Post[]) => void,
  space: string | null
): () => void {
  const postsCollection = collection(db, "posts");
  let q;

  if (space) {
    q = query(
      postsCollection, 
      where("space", "==", space), 
      orderBy("createdAt", "desc"), 
      limit(20)
    );
  } else {
    q = query(
      postsCollection, 
      orderBy("createdAt", "desc"), 
      limit(20)
    );
  }

  const unsubscribe = onSnapshot(q, (postsSnapshot) => {
    const posts: Post[] = postsSnapshot.docs.map(doc => {
        const data = doc.data();
        return {
            id: doc.id,
            ...data,
            createdAt: ((data.createdAt as Timestamp)?.toDate() || new Date()).toISOString(),
        } as Post;
    });
    callback(posts);
  },
  (error) => {
    console.error("Firestore 'posts' listener error:", error);
  });

  return unsubscribe;
}

export function listenToUserFollows(db: Firestore, userId: string, callback: (follows: Set<string>) => void): () => void {
    const followsQuery = query(collection(db, 'follows'), where('followerId', '==', userId));
    const unsubscribe = onSnapshot(followsQuery, (snapshot) => {
        const follows = new Set<string>();
        snapshot.forEach(doc => {
            follows.add(doc.data().followingId);
        });
        callback(follows);
    }, (error) => {
        console.error("Firestore 'follows' listener error:", error);
    });
    return unsubscribe;
}

export function listenToSpaces(
  db: Firestore,
  callback: (spaces: Space[]) => void,
  count: number
): () => void {
  const spacesQuery = query(
    collection(db, "spaces"), 
    where("visibility", "==", "public"),
    orderBy("createdAt", "desc"), 
    limit(count)
  );

  const unsubscribe = onSnapshot(spacesQuery, (snapshot) => {
    const spaces = snapshot.docs.map(doc => {
        const data = doc.data();
        return {
            id: doc.id,
            ...data,
            createdAt: ((data.createdAt as Timestamp)?.toDate() || new Date()).toISOString(),
        } as Space;
    });
    callback(spaces);
  },
  (error) => {
    console.error("Firestore 'spaces' listener error:", error);
  });

  return unsubscribe;
}
