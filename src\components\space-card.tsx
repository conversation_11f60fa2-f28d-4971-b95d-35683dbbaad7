
import type { Space } from '@/types/space';
import { <PERSON>, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Users, Lock, Globe, Tag } from 'lucide-react';
import Link from 'next/link';
import { Avatar, AvatarImage, AvatarFallback } from './ui/avatar';

function VisibilityIcon({ visibility }: { visibility: Space['visibility'] }) {
    switch (visibility) {
        case 'public':
            return <Globe className="h-4 w-4" />;
        case 'private':
        case 'secret':
            return <Lock className="h-4 w-4" />;
        default:
            return null;
    }
}

export default function SpaceCard({ space }: { space: Space }) {
    return (
        <Card className="border-2 border-black rounded-lg shadow-[4px_4px_0px_black] flex flex-col">
            <CardHeader>
                <div className="flex items-center gap-4">
                    <Avatar className="h-12 w-12 rounded-lg border-2 border-black">
                        <AvatarImage src={space.creatorAvatar} />
                        <AvatarFallback>{space.name.charAt(0)}</AvatarFallback>
                    </Avatar>
                    <div>
                        <CardTitle className="font-bold text-xl font-headline">{space.name}</CardTitle>
                        <CardDescription>Created by {space.creatorName}</CardDescription>
                    </div>
                </div>
            </CardHeader>
            <CardContent className="flex-grow">
                <p className="text-sm text-muted-foreground line-clamp-3">{space.description}</p>
                 <div className="flex flex-wrap gap-2 mt-4">
                    {space.tags?.slice(0, 3).map(tag => (
                        <Badge key={tag} variant="secondary" className="border border-black">
                            <Tag className="h-3 w-3 mr-1" />{tag}
                        </Badge>
                    ))}
                 </div>
            </CardContent>
            <CardFooter className="flex justify-between items-center border-t-2 border-black pt-4">
                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <div className="flex items-center gap-1">
                        <VisibilityIcon visibility={space.visibility} />
                        <span className="capitalize">{space.visibility}</span>
                    </div>
                    <div className="flex items-center gap-1">
                        <Users className="h-4 w-4" />
                        <span>{space.memberCount}</span>
                    </div>
                </div>
                <Button asChild className="bg-accent text-accent-foreground border-2 border-black shadow-[2px_2px_0px_#000]">
                    <Link href={`/spaces/${space.id}`}>View</Link>
                </Button>
            </CardFooter>
        </Card>
    );
}
