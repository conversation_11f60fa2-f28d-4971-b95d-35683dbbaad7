
'use client';

import Link from "next/link";
import { usePathname } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Logo } from "@/components/logo";
import { 
    Settings,
    Lightbulb,
    BookCheck,
    Folder,
    ShieldCheck,
    FileCheck,
    DollarSign,
    Gavel,
    Users,
    LineChart,
    FlaskConical,
    FileText,
    ClipboardCheck,
    Home,
    Bookmark,
    LayoutGrid,
    Upload
} from "lucide-react";
import { cn } from "@/lib/utils";
import { ScrollArea } from "@/components/ui/scroll-area";
import { UserNav } from "./user-nav";
import { ThemeToggle } from "./theme-toggle";
import { useScrollContext } from "@/context/scroll-context";

const NavLink = ({ href, icon: Icon, children }: { href: string; icon: React.ElementType; children: React.ReactNode }) => {
    const pathname = usePathname();
    const isActive = pathname.startsWith(href) && (href !== '/dashboard' || pathname === '/dashboard');

    return (
        <Button 
            asChild
            variant={isActive ? "secondary" : "ghost"}
            className={cn(
                "w-full justify-start text-sm gap-3 h-11 rounded-lg transition-all font-semibold px-3 border-2 border-black text-foreground whitespace-nowrap",
                isActive ? "bg-muted text-foreground" : "bg-primary/20 hover:bg-muted"
            )}
        >
            <Link href={href}>
                <Icon className="h-5 w-5" />
                <span className="truncate">{children}</span>
            </Link>
        </Button>
    );
};

const NavHeader = ({ children }: { children: React.ReactNode }) => (
    <h3 className="px-3 py-2 text-xs font-semibold text-foreground/70 tracking-wider uppercase whitespace-nowrap">
        {children}
    </h3>
);

export default function AppSidebar() {
  const { isScrolled } = useScrollContext();
  const isCollapsed = isScrolled;

  return (
    <div className={cn(
        "group flex h-full max-h-screen flex-col gap-2 text-foreground transition-all duration-300 ease-in-out z-20",
        isCollapsed 
            ? "w-0 md:w-5 bg-transparent hover:w-[320px] hover:bg-primary" 
            : "w-[320px] bg-primary"
      )}
    >
        <div className={cn(
            "flex h-full w-full flex-col overflow-hidden transition-opacity duration-300",
            isCollapsed ? "opacity-0 group-hover:opacity-100" : "opacity-100"
        )}>
            <div className="flex h-16 items-center border-b-2 border-black px-4 lg:h-[60px] lg:px-6">
                <Link href="/dashboard">
                  <Logo />
                </Link>
            </div>
            <div className="flex-1 min-h-0">
                <ScrollArea className="h-full">
                    <div className="px-4 py-2">
                        <UserNav />
                        <div className="mt-4 space-y-1">
                            <NavLink href="/dashboard" icon={Home}>Dashboard</NavLink>
                            <NavLink href="/saved" icon={Bookmark}>Saved</NavLink>
                            <NavLink href="/settings" icon={Settings}>Settings</NavLink>
                        </div>
                    </div>

                    <div className="px-4 py-2">
                        <NavHeader>Advanced Features</NavHeader>
                        <nav className="grid items-start font-medium gap-1">
                            <NavLink href="/spaces/discover" icon={LayoutGrid}>Discover Spaces</NavLink>
                            <NavLink href="/knowledge-bar" icon={Lightbulb}>Knowledge Bar</NavLink>
                            <NavLink href="/learning-spaces" icon={BookCheck}>Learning Spaces</NavLink>
                            <NavLink href="/collaborations" icon={Folder}>Collaborations</NavLink>
                            <NavLink href="/ai-moderation" icon={ShieldCheck}>AI Moderation</NavLink>
                            <NavLink href="/peer-review" icon={FileCheck}>Peer Review</NavLink>
                            <NavLink href="/grant-management" icon={DollarSign}>Grant Management</NavLink>
                            <NavLink href="/policy-center" icon={Gavel}>Policy Center</NavLink>
                            <NavLink href="/expert-network" icon={Users}>Expert Network</NavLink>
                            <NavLink href="/democracy-health" icon={LineChart}>Democracy Health</NavLink>
                        </nav>
                    </div>
                    <div className="px-4 py-2">
                        <NavHeader>Tools</NavHeader>
                        <nav className="grid items-start font-medium gap-1">
                            <NavLink href="/tools/hypothesis-generator" icon={FlaskConical}>Hypothesis Generator</NavLink>
                            <NavLink href="/tools/state-of-the-field" icon={FileText}>Field Synthesis</NavLink>
                            <NavLink href="/tools/fact-checker" icon={ClipboardCheck}>Fact-Checker</NavLink>
                            <NavLink href="/tools/media-upload" icon={Upload}>Media Upload Demo</NavLink>
                        </nav>
                    </div>
                </ScrollArea>
            </div>
            <div className="mt-auto flex flex-col items-center gap-2 border-t-2 border-black p-4">
                <ThemeToggle />
            </div>
        </div>
    </div>
  );
}
