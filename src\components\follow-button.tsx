
'use client';

import { useState, useTransition } from 'react';
import { Button } from './ui/button';
import { followAction } from '@/app/actions';
import { useToast } from '@/hooks/use-toast';
import { Loader2 } from 'lucide-react';

export default function FollowButton({
  isFollowed,
  targetUserId,
  currentUserId,
}: {
  isFollowed: boolean;
  targetUserId: string;
  currentUserId: string | null;
}) {
  const [isFollowedOptimistic, setIsFollowedOptimistic] = useState(isFollowed);
  const [isFollowPending, startFollowTransition] = useTransition();
  const { toast } = useToast();

  const handleFollow = () => {
    if (!currentUserId) {
      toast({ variant: 'destructive', title: 'Please log in to follow users.' });
      return;
    }
    if (currentUserId === targetUserId) return;

    startFollowTransition(async () => {
      setIsFollowedOptimistic(!isFollowedOptimistic);
      const result = await followAction(targetUserId, !isFollowedOptimistic);
      if (!result.success) {
        setIsFollowedOptimistic(isFollowed); // Revert on failure
        toast({ variant: 'destructive', title: 'Action failed', description: result.message });
      }
    });
  };

  if (currentUserId === targetUserId) {
    return null;
  }

  return (
    <Button
      variant={isFollowedOptimistic ? 'default' : 'outline'}
      onClick={handleFollow}
      disabled={isFollowPending}
      className="border-2 border-black rounded-full font-bold transition-all"
    >
      {isFollowPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
      {isFollowedOptimistic ? 'Following' : 'Follow'}
    </Button>
  );
}
