
'use client';
import { useState } from "react";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { MessageSquareQuote, PlusCircle, Radio, UserPlus, Zap, X, LayoutGrid } from "lucide-react";

export default function QuickActions() {
    const [isVisible, setIsVisible] = useState(true);

    if (!isVisible) {
        return null;
    }

    return (
        <Card className="border-2 border-black rounded-lg shadow-[4px_4px_0px_#000]">
            <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle className="font-bold flex items-center gap-2">
                    <Zap className="h-5 w-5" />
                    <span>Quick Actions</span>
                </CardTitle>
                <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8"
                    onClick={() => setIsVisible(false)}
                    aria-label="Close Quick Actions"
                >
                    <X className="h-5 w-5" />
                </Button>
            </CardHeader>
            <CardContent>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
                    <Button asChild variant="secondary" className="h-auto p-4 flex flex-col items-start gap-2 text-left border-2 border-black shadow-[2px_2px_0px_#000] hover:shadow-[4px_4px_0px_#000] transition-shadow">
                        <Link href="#">
                            <PlusCircle className="h-6 w-6 text-green-600" />
                            <span className="font-bold">Create Post</span>
                            <span className="text-xs text-muted-foreground">Share an update</span>
                        </Link>
                    </Button>
                     <Button asChild variant="secondary" className="h-auto p-4 flex flex-col items-start gap-2 text-left border-2 border-black shadow-[2px_2px_0px_#000] hover:shadow-[4px_4px_0px_#000] transition-shadow">
                        <Link href="/spaces/create">
                            <LayoutGrid className="h-6 w-6 text-purple-600" />
                            <span className="font-bold">Create Space</span>
                            <span className="text-xs text-muted-foreground">Build a new hub</span>
                        </Link>
                    </Button>
                    <Button asChild variant="secondary" className="h-auto p-4 flex flex-col items-start gap-2 text-left border-2 border-black shadow-[2px_2px_0px_#000] hover:shadow-[4px_4px_0px_#000] transition-shadow">
                        <Link href="#">
                            <MessageSquareQuote className="h-6 w-6 text-red-600" />
                            <span className="font-bold">Start Debate</span>
                            <span className="text-xs text-muted-foreground">Begin discussion</span>
                        </Link>
                    </Button>
                    <Button asChild variant="secondary" className="h-auto p-4 flex flex-col items-start gap-2 text-left border-2 border-black shadow-[2px_2px_0px_#000] hover:shadow-[4px_4px_0px_#000] transition-shadow">
                         <Link href="#">
                            <Radio className="h-6 w-6 text-orange-600" />
                            <span className="font-bold">Join Live Event</span>
                            <span className="text-xs text-muted-foreground">Attend session</span>
                         </Link>
                    </Button>
                    <Button asChild variant="secondary" className="h-auto p-4 flex flex-col items-start gap-2 text-left border-2 border-black shadow-[2px_2px_0px_#000] hover:shadow-[4px_4px_0px_#000] transition-shadow">
                         <Link href="#">
                            <UserPlus className="h-6 w-6 text-blue-600" />
                            <span className="font-bold">Find Connections</span>
                            <span className="text-xs text-muted-foreground">Expand network</span>
                         </Link>
                    </Button>
                </div>
            </CardContent>
        </Card>
    )
}
