
'use client';

import { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Sheet, Sheet<PERSON>ontent, SheetTrigger, SheetTitle } from "@/components/ui/sheet";
import { Search, Menu, MessageCircle, Users, TrendingUp, Medal, PlusCircle } from "lucide-react";
import AppSidebar from "@/components/app-sidebar";
import StatCard from "@/components/stat-card";
import QuickActions from "@/components/quick-actions";
import TrendingTopics from "@/components/trending-topics";
import { useScrollContext } from "@/context/scroll-context";
import { cn } from "@/lib/utils";
import CreatePostForm from '@/components/create-post-form';
import Feed from '@/components/feed';
import DiscoverSpacesWidget from './discover-spaces-widget';
import type { Post } from '@/types/post';

function AppHeader() {
  const [isCreatePostOpen, setCreatePostOpen] = useState(false);

  return (
    <header className="flex h-16 shrink-0 items-center gap-4 border-b-2 border-black bg-background px-4 md:px-6">
      <div className="flex items-center gap-4">
        <Sheet>
          <SheetTrigger asChild>
            <Button
              variant="outline"
              size="icon"
              className="shrink-0 md:hidden border-2 border-black"
            >
              <Menu className="h-5 w-5" />
              <span className="sr-only">Toggle navigation menu</span>
            </Button>
          </SheetTrigger>
          <SheetContent side="left" className="p-0 border-r-2 border-black bg-primary">
            <SheetTitle className="sr-only">Navigation Menu</SheetTitle>
            <AppSidebar />
          </SheetContent>
        </Sheet>
        <div>
          <h1 className="text-2xl font-bold font-headline">Dashboard</h1>
          <p className="text-sm text-muted-foreground">Professional Social Network</p>
        </div>
      </div>
      <div className="flex flex-1 items-center justify-end gap-2 md:gap-4">
        <div className="relative w-full max-w-sm">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search POLITICA..."
            className="pl-9 border-2 border-black rounded-lg focus:shadow-[2px_2px_0px_black] transition-shadow"
          />
        </div>
        <Dialog open={isCreatePostOpen} onOpenChange={setCreatePostOpen}>
          <DialogTrigger asChild>
            <Button className="border-2 border-black shadow-[2px_2px_0px_#000] hover:shadow-[4px_4px_0px_#000] transition-shadow bg-accent text-accent-foreground shrink-0">
              <PlusCircle className="mr-2" /> Create Post
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[625px] border-2 border-black shadow-[8px_8px_0px_black] rounded-lg">
            <DialogHeader>
              <DialogTitle className="font-headline text-2xl">Create a new post</DialogTitle>
              <DialogDescription>
                Share your insights with the network. Your post will be visible to others on the feed.
              </DialogDescription>
            </DialogHeader>
            <CreatePostForm setOpen={setCreatePostOpen} />
          </DialogContent>
        </Dialog>
        <Button variant="secondary" className="border-2 border-black shadow-[2px_2px_0px_#000] hover:shadow-[4px_4px_0px_#000] transition-shadow shrink-0 hidden md:inline-flex">
          Start Debate
        </Button>
      </div>
    </header>
  );
}

export default function DashboardClient({ initialPosts }: { initialPosts: Post[] }) {
  const { isScrolled } = useScrollContext();

  return (
    <>
      <AppHeader />
      <main className="grid gap-6 p-4 md:p-6 lg:grid-cols-3">
        <div className={cn("lg:col-span-2 space-y-6 transition-all duration-300", isScrolled && "lg:col-span-3")}>
            <div className={cn("grid gap-4 md:gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 transition-all duration-300 ease-in-out", isScrolled && "opacity-0 -translate-y-16 h-0 pointer-events-none")}>
                <StatCard icon={MessageCircle} title="Active Discussions" value="2,847" change="+12.5%" changeColor="bg-green-100 text-green-800 border-green-300" />
                <StatCard icon={Users} title="Network Connections" value="156" change="+8.2%" changeColor="bg-green-100 text-green-800 border-green-300" />
                <StatCard icon={Medal} title="Reputation Score" value="847" change="+15.3%" changeColor="bg-green-100 text-green-800 border-green-300" />
                <StatCard icon={TrendingUp} title="Weekly Engagement" value="94%" change="+5.1%" changeColor="bg-green-100 text-green-800 border-green-300" />
            </div>
            <div className={cn("transition-all duration-300 ease-in-out", isScrolled && "opacity-0 -translate-y-16 h-0 pointer-events-none")}>
                <QuickActions />
            </div>
            <Feed initialPosts={initialPosts} />
        </div>
        <div className={cn("lg:col-span-1 space-y-6 transition-all duration-300 ease-in-out", isScrolled && "opacity-0 w-0 pointer-events-none")}>
            <DiscoverSpacesWidget />
            <TrendingTopics />
        </div>
      </main>
    </>
  );
}
