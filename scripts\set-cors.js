const { exec } = require('child_process');
const path = require('path');

// CORS configuration
const corsConfig = [
  {
    "origin": [
      "http://localhost:3000",
      "http://localhost:3001", 
      "https://localhost:3000",
      "https://localhost:3001",
      "https://politica-overview.web.app",
      "https://politica-overview.firebaseapp.com",
      "https://politica-overview.firebasestorage.app"
    ],
    "method": [
      "GET",
      "POST", 
      "PUT",
      "DELETE",
      "HEAD",
      "OPTIONS"
    ],
    "maxAgeSeconds": 3600,
    "responseHeader": [
      "Content-Type",
      "Authorization",
      "Content-Length",
      "User-Agent",
      "X-Requested-With",
      "X-Firebase-Storage-Version",
      "X-Firebase-GMPID"
    ]
  }
];

console.log('Setting CORS configuration for Firebase Storage...');

// Try using Firebase CLI to set CORS
const corsPath = path.join(__dirname, '..', 'cors.json');
const bucketName = 'politica-overview.firebasestorage.app';

// First try with gsutil if available
exec(`gsutil cors set "${corsPath}" gs://${bucketName}`, (error, stdout, stderr) => {
  if (error) {
    console.log('gsutil not available, trying alternative method...');
    
    // Alternative: Use Firebase Admin SDK
    try {
      const admin = require('firebase-admin');
      
      if (!admin.apps.length) {
        // Initialize with service account if available
        const serviceAccount = process.env.FIREBASE_SERVICE_ACCOUNT_KEY;
        if (serviceAccount) {
          admin.initializeApp({
            credential: admin.credential.cert(JSON.parse(serviceAccount)),
            storageBucket: bucketName
          });
        } else {
          console.log('No service account key found. Please set CORS manually.');
          console.log('Run this command:');
          console.log(`gsutil cors set cors.json gs://${bucketName}`);
          process.exit(1);
        }
      }
      
      const bucket = admin.storage().bucket();
      
      // Set CORS configuration
      bucket.setCorsConfiguration(corsConfig).then(() => {
        console.log('✅ CORS configuration set successfully!');
        process.exit(0);
      }).catch((err) => {
        console.error('❌ Failed to set CORS configuration:', err.message);
        console.log('\nManual setup required:');
        console.log(`1. Install Google Cloud SDK`);
        console.log(`2. Run: gsutil cors set cors.json gs://${bucketName}`);
        process.exit(1);
      });
      
    } catch (adminError) {
      console.log('Firebase Admin SDK not available.');
      console.log('\nManual CORS setup required:');
      console.log(`1. Install Google Cloud SDK`);
      console.log(`2. Authenticate: gcloud auth login`);
      console.log(`3. Run: gsutil cors set cors.json gs://${bucketName}`);
      console.log('\nOr set CORS in Google Cloud Console:');
      console.log(`https://console.cloud.google.com/storage/browser/${bucketName}`);
      process.exit(1);
    }
  } else {
    console.log('✅ CORS configuration set successfully with gsutil!');
    console.log(stdout);
    process.exit(0);
  }
});
