
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const sessionCookie = request.cookies.get('session')?.value;
  
  const isAuthPage = pathname === '/';
  
  // If the user has a session cookie and is trying to access the login page,
  // redirect them to the dashboard.
  if (sessionCookie && isAuthPage) {
    return NextResponse.redirect(new URL('/dashboard', request.url));
  }

  // If the user does not have a session cookie and is trying to access a protected route,
  // redirect them to the login page.
  if (!sessionCookie && !isAuthPage) {
    return NextResponse.redirect(new URL('/', request.url));
  }

  // Otherwise, allow the request to proceed.
  return NextResponse.next();
}

export const config = {
  // Match all routes except for static files, api routes, and image assets
  matcher: ['/((?!api|_next/static|_next/image|favicon.ico).*)'],
};
