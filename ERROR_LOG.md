# Application Error & Solution Log

This document serves as a manual log for tracking significant errors encountered during development, the attempts made to fix them, and the final resolutions.

---

**Date:** YYYY-MM-DD

### Error Description:
(Provide a clear and concise description of the error. Include console logs, screenshots, or any relevant details that help in understanding the problem.)

### Troubleshooting & Trials:
1.  **Attempt 1:** (Describe the first approach to fix the error.)
    *   **Reasoning:** (Why was this approach chosen?)
    *   **Outcome:** (What was the result? Did it work, fail, or lead to another error?)
2.  **Attempt 2:** (Describe the next approach.)
    *   **Reasoning:**
    *   **Outcome:**

### Final Solution:
(Provide a detailed explanation of the final fix that resolved the error. Include code snippets if necessary and explain why this solution works.)

---

**Date:** 2024-07-10

### Error Description:
During `next build`, the process consistently failed with the error: `Module build failed: UnhandledSchemeError: Reading from "node:process"`. This error indicates that a package intended for a Node.js server environment (like `firebase-admin` or `@genkit-ai/googleai`) was being incorrectly included in the client-side JavaScript bundle, which is sent to the browser. The browser does not have access to Node.js APIs like `process`, causing the build to crash.

### Troubleshooting & Trials:
1.  **Attempt 1: `next.config.js` Webpack Fallback.**
    *   **Reasoning:** The standard Next.js approach for this issue is to configure the Webpack bundler to replace server-only packages with empty modules when creating the client bundle.
    *   **Outcome:** The error persisted. This indicated that the issue was more deeply rooted in the application's import graph than a simple configuration fix could solve, or that another issue was preventing the configuration from being effective.

2.  **Attempt 2: `'use server';` Directive.**
    *   **Reasoning:** To create an explicit and unambiguous boundary, the `'use server';` directive was added to files like `src/lib/firestore.ts` that directly imported `firebase-admin`. This directive tells Next.js to never include the module in any client code.
    *   **Outcome:** The error still persisted. This was a critical clue. It meant that a file that is *not allowed* to import server-only code was doing so. The investigation then pointed towards `middleware.ts`, as middleware runs in a special, restricted environment (the "Edge Runtime").

### Final Solution:
The root cause was architectural: `middleware.ts` was importing a function (`getSession`) from `src/lib/session.ts`, which was correctly marked with `'use server';`. **Next.js middleware is forbidden from importing code from a file that uses the `'use server';` directive.**

The final, successful solution was to refactor the authentication logic to respect this constraint:

1.  **Isolate the Middleware:** The `middleware.ts` file was rewritten to be completely self-contained. It no longer imports any server-only code. Instead, it uses the lightweight and runtime-agnostic `jose` library to perform a basic, fast verification of the session cookie. This is compatible with the Edge Runtime.

2.  **Secure Server-Side Verification:** The `getSession` function in `src/lib/session.ts` (using the full `firebase-admin` SDK) was kept as the single source of truth for secure verification in Server Components and Server Actions, where it is safe to use.

This separation of concerns resolved the architectural conflict, allowing the build to succeed. The middleware can now protect pages without violating build rules, and the rest of the application can securely verify the user's identity.
