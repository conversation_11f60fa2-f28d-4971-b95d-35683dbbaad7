'use server';

/**
 * @fileOverview Analyzes and synthesizes public comments to provide a summarized overview of key arguments and sentiments.
 *
 * - analyzeComments - A function that handles the comment analysis process.
 * - AnalyzeCommentsInput - The input type for the analyzeComments function.
 * - AnalyzeCommentsOutput - The return type for the analyzeComments function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const AnalyzeCommentsInputSchema = z.object({
  comments: z.string().describe('A collection of public comments to analyze.'),
});
export type AnalyzeCommentsInput = z.infer<typeof AnalyzeCommentsInputSchema>;

const AnalyzeCommentsOutputSchema = z.object({
  summary: z.string().describe('A summarized overview of the key arguments and sentiments expressed in the comments.'),
  keyArguments: z.array(z.string()).describe('A list of key arguments identified in the comments.'),
  overallSentiment: z.string().describe('The overall sentiment expressed in the comments (e.g., positive, negative, mixed).'),
});
export type AnalyzeCommentsOutput = z.infer<typeof AnalyzeCommentsOutputSchema>;

export async function analyzeComments(input: AnalyzeCommentsInput): Promise<AnalyzeCommentsOutput> {
  return analyzeCommentsFlow(input);
}

const prompt = ai.definePrompt({
  name: 'analyzeCommentsPrompt',
  input: {schema: AnalyzeCommentsInputSchema},
  output: {schema: AnalyzeCommentsOutputSchema},
  prompt: `You are an expert in analyzing public comments and synthesizing information.

You will be provided with a collection of comments. Your task is to analyze these comments and provide a summarized overview of the key arguments and sentiments expressed.

Comments:
{{{comments}}}

Instructions:
1.  Identify the key arguments presented in the comments.
2.  Determine the overall sentiment expressed in the comments (e.g., positive, negative, mixed).
3.  Provide a concise summary of the key arguments and sentiments.
4.  Format the output as a JSON object with the following fields:
    - summary: A summarized overview of the key arguments and sentiments.
    - keyArguments: A list of key arguments identified in the comments.
    - overallSentiment: The overall sentiment expressed in the comments.

Output:`,
});

const analyzeCommentsFlow = ai.defineFlow(
  {
    name: 'analyzeCommentsFlow',
    inputSchema: AnalyzeCommentsInputSchema,
    outputSchema: AnalyzeCommentsOutputSchema,
  },
  async input => {
    const {output} = await prompt(input);
    return output!;
  }
);
