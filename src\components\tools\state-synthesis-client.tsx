
"use client";

import { useFormStatus } from "react-dom";
import { useActionState, useEffect } from "react";
import { stateOfTheFieldSynthesisAction } from "@/app/actions";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { Bot, Loader2 } from "lucide-react";

const initialState = {
  message: "",
  data: null,
  errors: null,
};

function SubmitButton() {
  const { pending } = useFormStatus();
  return (
    <Button 
      type="submit" 
      aria-disabled={pending} 
      disabled={pending}
      className="bg-accent text-accent-foreground border-2 border-black shadow-[2px_2px_0px_black] hover:shadow-[4px_4px_0px_black] active:shadow-[1px_1px_0px_black] transition-all w-full md:w-auto"
    >
      {pending ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Bot className="mr-2 h-4 w-4" />}
      {pending ? "Synthesizing..." : "Synthesize Field"}
    </Button>
  );
}

export default function StateSynthesisClient() {
  const [state, formAction] = useActionState(stateOfTheFieldSynthesisAction, initialState);
  const { toast } = useToast();

  useEffect(() => {
    if (state.message && state.message !== 'success') {
      toast({
        variant: "destructive",
        title: "Error",
        description: state.message,
      });
    }
  }, [state, toast]);

  return (
    <div className="grid gap-6">
      <Card className="border-2 border-black shadow-[4px_4px_0px_black] rounded-lg">
        <CardHeader>
          <CardTitle className="font-headline">Enter a Research Topic</CardTitle>
          <CardDescription>
            Provide a topic to receive a synthesis of the current state of research, including key trends, gaps, and controversies.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form action={formAction} className="space-y-4">
            <Textarea
              name="topic"
              placeholder="e.g., 'Advancements in carbon capture and storage technology'"
              className="min-h-[100px] border-2 border-black focus:shadow-[2px_2px_0px_black] transition-shadow"
            />
             {state.errors?.topic && <p className="text-sm font-medium text-destructive">{state.errors.topic}</p>}
            <SubmitButton />
          </form>
        </CardContent>
      </Card>

      {state.data && (
        <Card className="border-2 border-black shadow-[4px_4px_0px_black] rounded-lg">
            <CardHeader>
                <CardTitle className="font-headline">Synthesis Report</CardTitle>
                <CardDescription>AI-generated analysis of the research field.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
                <div>
                    <h3 className="font-headline text-lg mb-2">Summary</h3>
                    <p className="text-base">{state.data.summary}</p>
                </div>
                 <div>
                    <h3 className="font-headline text-lg mb-2">Key Trends</h3>
                    <p className="text-base">{state.data.keyTrends}</p>
                </div>
                 <div>
                    <h3 className="font-headline text-lg mb-2">Research Gaps</h3>
                    <p className="text-base">{state.data.gaps}</p>
                </div>
                 <div>
                    <h3 className="font-headline text-lg mb-2">Controversies</h3>
                    <p className="text-base">{state.data.controversies}</p>
                </div>
            </CardContent>
        </Card>
      )}
    </div>
  );
}
