'use client';

import { useState } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Edit, PlusCircle, Trash2, Loader2 } from 'lucide-react';
import { updateUserProfileAction } from '@/app/actions';
import type { UserProfile } from '@/types/user';
import { useToast } from "@/hooks/use-toast";
import { ScrollArea } from './ui/scroll-area';

const profileSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters long.'),
  title: z.string().min(5, 'Title must be at least 5 characters long.'),
  career: z.array(z.object({
    role: z.string().min(2, 'Role is required.'),
    company: z.string().min(2, 'Company is required.'),
    period: z.string().min(2, 'Period is required.'),
  })).optional(),
  skills: z.array(z.object({
      name: z.string().min(1, 'Skill name is required.'),
      endorsements: z.number().int(),
  })).optional(),
});

export default function EditProfileForm({ profile }: { profile: UserProfile }) {
  const [isOpen, setIsOpen] = useState(false);
  const [isPending, setIsPending] = useState(false);
  const { toast } = useToast();

  const form = useForm<z.infer<typeof profileSchema>>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      name: profile.name,
      title: profile.title,
      career: profile.career,
      skills: profile.skills,
    },
  });

  const { fields: careerFields, append: appendCareer, remove: removeCareer } = useFieldArray({
    control: form.control,
    name: "career",
  });
  
  const { fields: skillFields, append: appendSkill, remove: removeSkill } = useFieldArray({
    control: form.control,
    name: "skills",
  });

  const onSubmit = async (values: z.infer<typeof profileSchema>) => {
    setIsPending(true);
    const formData = new FormData();
    formData.append('profileData', JSON.stringify(values));

    const result = await updateUserProfileAction(null, formData);
    
    setIsPending(false);

    if (result.message === 'success') {
      toast({ title: "Profile Updated", description: "Your profile has been successfully updated." });
      setIsOpen(false);
    } else {
      toast({ variant: "destructive", title: "Update Failed", description: result.message });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className="border-2 border-black shadow-[2px_2px_0px_#000] hover:shadow-[4px_4px_0px_#000] transition-shadow">
          <Edit className="mr-2 h-4 w-4" />
          Edit Profile
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[625px] border-2 border-black shadow-[8px_8px_0px_black] rounded-lg">
        <DialogHeader>
          <DialogTitle className="font-headline text-2xl">Edit Profile</DialogTitle>
          <DialogDescription>
            Make changes to your profile here. Click save when you're done.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <ScrollArea className="max-h-[60vh] p-1 pr-4">
            <div className="space-y-6 py-4">
              <div className="space-y-2">
                <Label htmlFor="name">Full Name</Label>
                <Input id="name" {...form.register('name')} className="border-2 border-black" />
                {form.formState.errors.name && <p className="text-sm text-destructive">{form.formState.errors.name.message}</p>}
              </div>
              <div className="space-y-2">
                <Label htmlFor="title">Title / Headline</Label>
                <Input id="title" {...form.register('title')} className="border-2 border-black" />
                {form.formState.errors.title && <p className="text-sm text-destructive">{form.formState.errors.title.message}</p>}
              </div>

              <div className="space-y-4">
                <Label className="text-lg font-headline">Career History</Label>
                {careerFields.map((field, index) => (
                  <div key={field.id} className="grid grid-cols-[1fr_auto] gap-4 items-end p-4 border-2 border-black rounded-md bg-muted/50">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
                        <div>
                            <Label>Role</Label>
                            <Input {...form.register(`career.${index}.role`)} className="border-2 border-black bg-background" />
                        </div>
                        <div>
                            <Label>Company</Label>
                            <Input {...form.register(`career.${index}.company`)} className="border-2 border-black bg-background" />
                        </div>
                        <div>
                            <Label>Period</Label>
                            <Input {...form.register(`career.${index}.period`)} className="border-2 border-black bg-background" />
                        </div>
                    </div>
                    <Button type="button" variant="destructive" size="icon" onClick={() => removeCareer(index)}><Trash2 className="h-4 w-4"/></Button>
                  </div>
                ))}
                <Button type="button" variant="outline" size="sm" onClick={() => appendCareer({ role: '', company: '', period: '' })}>
                  <PlusCircle className="mr-2 h-4 w-4" /> Add Career Item
                </Button>
              </div>
              
              <div className="space-y-4">
                <Label className="text-lg font-headline">Skills</Label>
                {skillFields.map((field, index) => (
                  <div key={field.id} className="grid grid-cols-[1fr_auto] gap-4 items-end p-4 border-2 border-black rounded-md bg-muted/50">
                     <div>
                        <Label>Skill Name</Label>
                        <Input {...form.register(`skills.${index}.name`)} className="border-2 border-black bg-background" />
                     </div>
                     <Button type="button" variant="destructive" size="icon" onClick={() => removeSkill(index)}><Trash2 className="h-4 w-4"/></Button>
                  </div>
                ))}
                 <Button type="button" variant="outline" size="sm" onClick={() => appendSkill({ name: '', endorsements: 0 })}>
                  <PlusCircle className="mr-2 h-4 w-4" /> Add Skill
                </Button>
              </div>

            </div>
          </ScrollArea>
          <DialogFooter className="mt-6">
            <DialogClose asChild>
                <Button type="button" variant="secondary" className="border-2 border-black">Cancel</Button>
            </DialogClose>
            <Button type="submit" disabled={isPending} className="bg-accent text-accent-foreground border-2 border-black shadow-[2px_2px_0px_#000]">
                {isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Save Changes
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
