import { getStorage, ref, uploadBytesResumable, getDownloadURL, deleteObject, connectStorageEmulator } from 'firebase/storage';
import firebaseApp from './firebase';
import { generateUniqueFileName } from './image-compression';
import type { MediaItem } from '@/types/post';

export interface UploadProgress {
  progress: number;
  stage: 'uploading' | 'complete' | 'error';
  error?: string;
}

export interface UploadResult {
  url: string;
  fileName: string;
  fileSize: number;
  originalSize: number;
  mimeType: string;
  width?: number;
  height?: number;
}

// Initialize Firebase Storage
const storage = getStorage(firebaseApp);

// Configure storage for development
if (typeof window !== 'undefined' && window.location.hostname === 'localhost') {
  // Add custom headers for CORS in development
  console.log('Development mode: Firebase Storage configured for localhost');
}

/**
 * Uploads a file to Firebase Storage with progress tracking
 */
export async function uploadImage(
  file: File,
  originalSize: number,
  dimensions?: { width: number; height: number },
  onProgress?: (progress: UploadProgress) => void
): Promise<UploadResult> {
  try {
    // Generate unique filename
    const fileName = generateUniqueFileName(file.name);
    const storageRef = ref(storage, `posts/images/${fileName}`);

    // Create upload task with metadata for better CORS handling
    const metadata = {
      contentType: file.type,
      customMetadata: {
        'originalSize': originalSize.toString(),
        'uploadedAt': new Date().toISOString(),
        'width': dimensions?.width?.toString() || '',
        'height': dimensions?.height?.toString() || ''
      }
    };

    const uploadTask = uploadBytesResumable(storageRef, file, metadata);

    return new Promise((resolve, reject) => {
      uploadTask.on(
        'state_changed',
        (snapshot) => {
          // Progress tracking
          const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
          onProgress?.({
            progress: Math.round(progress),
            stage: 'uploading'
          });
        },
        (error) => {
          // Handle upload errors
          const errorMessage = getStorageErrorMessage(error);
          onProgress?.({
            progress: 0,
            stage: 'error',
            error: errorMessage
          });
          reject(new Error(errorMessage));
        },
        async () => {
          try {
            // Upload completed successfully
            const downloadURL = await getDownloadURL(uploadTask.snapshot.ref);
            
            onProgress?.({
              progress: 100,
              stage: 'complete'
            });

            resolve({
              url: downloadURL,
              fileName,
              fileSize: file.size,
              originalSize,
              mimeType: file.type,
              width: dimensions?.width,
              height: dimensions?.height
            });
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Failed to get download URL';
            onProgress?.({
              progress: 0,
              stage: 'error',
              error: errorMessage
            });
            reject(new Error(errorMessage));
          }
        }
      );
    });
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Upload failed';
    onProgress?.({
      progress: 0,
      stage: 'error',
      error: errorMessage
    });
    throw new Error(`Upload failed: ${errorMessage}`);
  }
}

/**
 * Uploads multiple images with progress tracking
 */
export async function uploadImages(
  files: { file: File; originalSize: number; dimensions?: { width: number; height: number } }[],
  onProgress?: (fileIndex: number, progress: UploadProgress) => void
): Promise<UploadResult[]> {
  const results: UploadResult[] = [];
  
  for (let i = 0; i < files.length; i++) {
    const { file, originalSize, dimensions } = files[i];
    
    try {
      const result = await uploadImage(
        file,
        originalSize,
        dimensions,
        (progress) => onProgress?.(i, progress)
      );
      results.push(result);
    } catch (error) {
      console.error(`Failed to upload ${file.name}:`, error);
      throw error; // Re-throw to let the caller handle it
    }
  }
  
  return results;
}

/**
 * Deletes an image from Firebase Storage
 */
export async function deleteImage(fileName: string): Promise<void> {
  try {
    const storageRef = ref(storage, `posts/images/${fileName}`);
    await deleteObject(storageRef);
  } catch (error) {
    console.error('Failed to delete image:', error);
    throw new Error('Failed to delete image');
  }
}

/**
 * Deletes multiple images from Firebase Storage
 */
export async function deleteImages(fileNames: string[]): Promise<void> {
  const deletePromises = fileNames.map(fileName => deleteImage(fileName));
  await Promise.all(deletePromises);
}

/**
 * Converts UploadResult to MediaItem
 */
export function uploadResultToMediaItem(result: UploadResult, id: string): MediaItem {
  return {
    id,
    url: result.url,
    fileName: result.fileName,
    fileSize: result.fileSize,
    originalSize: result.originalSize,
    mimeType: result.mimeType,
    width: result.width,
    height: result.height
  };
}

/**
 * Gets a user-friendly error message from Firebase Storage error
 */
function getStorageErrorMessage(error: any): string {
  switch (error.code) {
    case 'storage/unauthorized':
      return 'You do not have permission to upload files';
    case 'storage/canceled':
      return 'Upload was canceled';
    case 'storage/quota-exceeded':
      return 'Storage quota exceeded';
    case 'storage/invalid-format':
      return 'Invalid file format';
    case 'storage/invalid-event-name':
      return 'Invalid upload event';
    case 'storage/invalid-url':
      return 'Invalid storage URL';
    case 'storage/invalid-argument':
      return 'Invalid upload argument';
    case 'storage/no-default-bucket':
      return 'No default storage bucket configured';
    case 'storage/cannot-slice-blob':
      return 'File cannot be processed';
    case 'storage/server-file-wrong-size':
      return 'File size mismatch on server';
    default:
      return error.message || 'Upload failed';
  }
}

/**
 * Validates storage configuration
 */
export function validateStorageConfig(): boolean {
  try {
    const storage = getStorage(firebaseApp);
    return !!storage;
  } catch (error) {
    console.error('Firebase Storage not properly configured:', error);
    return false;
  }
}

/**
 * Development helper to check CORS configuration
 */
export async function testCorsConfiguration(): Promise<boolean> {
  try {
    // Try to create a simple reference to test CORS
    const testRef = ref(storage, 'test/cors-test.txt');
    console.log('CORS test reference created successfully');
    return true;
  } catch (error) {
    console.error('CORS configuration test failed:', error);
    return false;
  }
}
