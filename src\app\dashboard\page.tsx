
import PageLayout from "@/components/page-layout";
import DashboardClient from "@/components/dashboard-client";
import { getPosts } from "@/lib/firestore";
import { getSession } from "@/lib/session";

export default async function DashboardPage() {
  const session = await getSession();
  // Fetch initial posts on the server. The Feed component will be simpler.
  const initialPosts = await getPosts(null, session?.uid);

  return (
    <PageLayout>
      <DashboardClient initialPosts={initialPosts} />
    </PageLayout>
  );
}
