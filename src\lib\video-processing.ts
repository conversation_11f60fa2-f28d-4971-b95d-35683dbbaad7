export interface VideoCompressionOptions {
  maxSizeMB?: number;
  maxDurationSeconds?: number;
  quality?: 'low' | 'medium' | 'high';
  maxWidth?: number;
  maxHeight?: number;
  frameRate?: number;
}

export interface VideoCompressionResult {
  compressedFile: File;
  originalSize: number;
  compressedSize: number;
  compressionRatio: number;
  width: number;
  height: number;
  duration: number;
  frameRate: number;
  thumbnail?: File;
}

export interface VideoCompressionProgress {
  progress: number;
  stage: 'analyzing' | 'compressing' | 'generating-thumbnail' | 'complete' | 'error';
  error?: string;
  currentTime?: number;
  totalTime?: number;
}

const DEFAULT_VIDEO_OPTIONS: VideoCompressionOptions = {
  maxSizeMB: 50, // 50MB max file size for videos
  maxDurationSeconds: 300, // 5 minutes max
  quality: 'medium',
  maxWidth: 1280,
  maxHeight: 720,
  frameRate: 30,
};

const SUPPORTED_VIDEO_TYPES = [
  'video/mp4',
  'video/webm',
  'video/ogg',
  'video/quicktime',
  'video/x-msvideo', // .avi
];

/**
 * Validates if the file is a supported video format
 */
export function isValidVideoFile(file: File): boolean {
  return SUPPORTED_VIDEO_TYPES.includes(file.type);
}

/**
 * Gets video metadata (duration, dimensions, etc.)
 */
export function getVideoMetadata(file: File): Promise<{
  duration: number;
  width: number;
  height: number;
  frameRate: number;
}> {
  return new Promise((resolve, reject) => {
    const video = document.createElement('video');
    const url = URL.createObjectURL(file);
    
    video.onloadedmetadata = () => {
      const metadata = {
        duration: video.duration,
        width: video.videoWidth,
        height: video.videoHeight,
        frameRate: 30, // Default, actual frame rate detection is complex
      };
      
      URL.revokeObjectURL(url);
      resolve(metadata);
    };
    
    video.onerror = () => {
      URL.revokeObjectURL(url);
      reject(new Error('Failed to load video metadata'));
    };
    
    video.src = url;
  });
}

/**
 * Generates a thumbnail from a video file
 */
export function generateVideoThumbnail(
  file: File,
  timeSeconds: number = 1
): Promise<File> {
  return new Promise((resolve, reject) => {
    const video = document.createElement('video');
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const url = URL.createObjectURL(file);
    
    if (!ctx) {
      reject(new Error('Canvas context not available'));
      return;
    }
    
    video.onloadedmetadata = () => {
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      
      video.currentTime = Math.min(timeSeconds, video.duration);
    };
    
    video.onseeked = () => {
      ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
      
      canvas.toBlob((blob) => {
        if (blob) {
          const thumbnailFile = new File(
            [blob],
            `${file.name.split('.')[0]}_thumbnail.jpg`,
            { type: 'image/jpeg' }
          );
          URL.revokeObjectURL(url);
          resolve(thumbnailFile);
        } else {
          URL.revokeObjectURL(url);
          reject(new Error('Failed to generate thumbnail'));
        }
      }, 'image/jpeg', 0.8);
    };
    
    video.onerror = () => {
      URL.revokeObjectURL(url);
      reject(new Error('Failed to load video for thumbnail generation'));
    };
    
    video.src = url;
  });
}

/**
 * Compresses a video file (simplified version - in production you'd use FFmpeg.wasm)
 * For now, this mainly validates and prepares the video for upload
 */
export async function compressVideo(
  file: File,
  options: VideoCompressionOptions = {},
  onProgress?: (progress: VideoCompressionProgress) => void
): Promise<VideoCompressionResult> {
  const finalOptions = { ...DEFAULT_VIDEO_OPTIONS, ...options };
  
  try {
    // Validate file type
    if (!isValidVideoFile(file)) {
      throw new Error('Unsupported video format. Please use MP4, WebM, or MOV files.');
    }
    
    onProgress?.({
      progress: 10,
      stage: 'analyzing'
    });
    
    // Get video metadata
    const metadata = await getVideoMetadata(file);
    
    // Validate duration
    if (finalOptions.maxDurationSeconds && metadata.duration > finalOptions.maxDurationSeconds) {
      throw new Error(`Video duration (${Math.round(metadata.duration)}s) exceeds maximum allowed (${finalOptions.maxDurationSeconds}s)`);
    }
    
    // Validate file size
    const fileSizeMB = file.size / (1024 * 1024);
    if (finalOptions.maxSizeMB && fileSizeMB > finalOptions.maxSizeMB) {
      throw new Error(`Video size (${fileSizeMB.toFixed(1)}MB) exceeds maximum allowed (${finalOptions.maxSizeMB}MB)`);
    }
    
    onProgress?.({
      progress: 50,
      stage: 'compressing'
    });
    
    // Check if compression is needed
    const needsCompression = (
      (finalOptions.maxSizeMB && fileSizeMB > finalOptions.maxSizeMB) ||
      (finalOptions.maxWidth && metadata.width > finalOptions.maxWidth) ||
      (finalOptions.maxHeight && metadata.height > finalOptions.maxHeight)
    );

    let compressedFile = file;

    if (needsCompression) {
      try {
        // Try to compress with FFmpeg.wasm
        compressedFile = await compressVideoWithFFmpeg(file, finalOptions, onProgress);
      } catch (ffmpegError) {
        console.warn('FFmpeg compression failed, using fallback:', ffmpegError);

        // Fallback: If file is too large, reject it
        if (finalOptions.maxSizeMB && fileSizeMB > finalOptions.maxSizeMB) {
          throw new Error(`Video size (${fileSizeMB.toFixed(1)}MB) exceeds maximum allowed (${finalOptions.maxSizeMB}MB). Please compress the video manually.`);
        }

        // Otherwise, use original file
        compressedFile = file;
      }
    }
    
    onProgress?.({
      progress: 80,
      stage: 'generating-thumbnail'
    });
    
    // Generate thumbnail
    const thumbnail = await generateVideoThumbnail(file);
    
    onProgress?.({
      progress: 100,
      stage: 'complete'
    });
    
    return {
      compressedFile,
      originalSize: file.size,
      compressedSize: compressedFile.size,
      compressionRatio: file.size / compressedFile.size,
      width: metadata.width,
      height: metadata.height,
      duration: metadata.duration,
      frameRate: metadata.frameRate,
      thumbnail,
    };
    
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Video processing failed';
    onProgress?.({
      progress: 0,
      stage: 'error',
      error: errorMessage
    });
    throw error;
  }
}

/**
 * Formats video duration for display
 */
export function formatVideoDuration(seconds: number): string {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
}

/**
 * Gets video file size in human readable format
 */
export function formatVideoSize(bytes: number): string {
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  if (bytes === 0) return '0 Bytes';
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
}

/**
 * Generates a unique filename for video uploads
 */
export function generateUniqueVideoFileName(originalName: string): string {
  const timestamp = Date.now();
  const randomString = Math.random().toString(36).substring(2, 8);
  const extension = originalName.split('.').pop() || 'mp4';
  const baseName = originalName.split('.')[0].replace(/[^a-zA-Z0-9]/g, '_');
  return `${baseName}_${timestamp}_${randomString}.${extension}`;
}

/**
 * Compresses video using FFmpeg.wasm
 */
async function compressVideoWithFFmpeg(
  file: File,
  options: VideoCompressionOptions,
  onProgress?: (progress: VideoCompressionProgress) => void
): Promise<File> {
  // Dynamic import to avoid SSR issues
  const { FFmpeg } = await import('@ffmpeg/ffmpeg');
  const { fetchFile, toBlobURL } = await import('@ffmpeg/util');

  const ffmpeg = new FFmpeg();

  // Load FFmpeg
  const baseURL = 'https://unpkg.com/@ffmpeg/core@0.12.6/dist/umd';
  await ffmpeg.load({
    coreURL: await toBlobURL(`${baseURL}/ffmpeg-core.js`, 'text/javascript'),
    wasmURL: await toBlobURL(`${baseURL}/ffmpeg-core.wasm`, 'application/wasm'),
  });

  // Set up progress tracking
  ffmpeg.on('progress', ({ progress }) => {
    onProgress?.({
      progress: 30 + (progress * 0.4), // 30-70% for compression
      stage: 'compressing'
    });
  });

  // Write input file
  const inputName = 'input.mp4';
  const outputName = 'output.mp4';
  await ffmpeg.writeFile(inputName, await fetchFile(file));

  // Build FFmpeg command based on options
  const args = ['-i', inputName];

  // Video codec and quality
  args.push('-c:v', 'libx264');

  if (options.quality === 'low') {
    args.push('-crf', '28');
  } else if (options.quality === 'high') {
    args.push('-crf', '18');
  } else {
    args.push('-crf', '23'); // medium quality
  }

  // Resolution scaling
  if (options.maxWidth && options.maxHeight) {
    args.push('-vf', `scale='min(${options.maxWidth},iw)':'min(${options.maxHeight},ih)':force_original_aspect_ratio=decrease`);
  }

  // Frame rate
  if (options.frameRate) {
    args.push('-r', options.frameRate.toString());
  }

  // Audio codec
  args.push('-c:a', 'aac', '-b:a', '128k');

  // Output format
  args.push('-f', 'mp4', outputName);

  // Execute compression
  await ffmpeg.exec(args);

  // Read output file
  const data = await ffmpeg.readFile(outputName);
  const compressedBlob = new Blob([data], { type: 'video/mp4' });

  // Clean up
  await ffmpeg.deleteFile(inputName);
  await ffmpeg.deleteFile(outputName);

  return new File([compressedBlob], file.name, { type: 'video/mp4' });
}
