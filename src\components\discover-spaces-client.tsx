
'use client';

import type { Space } from '@/types/space';
import SpaceCard from '@/components/space-card';
import { useState, useMemo } from 'react';
import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";
import { AlertTriangle } from 'lucide-react';

export default function DiscoverSpacesClient({ spaces }: { spaces: Space[] }) {
    const [searchTerm, setSearchTerm] = useState('');
    
    const filteredSpaces = useMemo(() => {
        if (!searchTerm) return spaces;
        const lowercasedTerm = searchTerm.toLowerCase();
        return spaces.filter(space => 
            space.name.toLowerCase().includes(lowercasedTerm) ||
            space.description.toLowerCase().includes(lowercasedTerm) ||
            space.category.toLowerCase().includes(lowercasedTerm) ||
            space.tags.some(tag => tag.toLowerCase().includes(lowercasedTerm))
        );
    }, [spaces, searchTerm]);
    
    return (
        <>
            <div className="mb-6">
                <div className="relative">
                    <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input 
                        placeholder="Search spaces by name, category, or tags..." 
                        className="pl-9 border-2 border-black" 
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                    />
                </div>
            </div>
            {filteredSpaces.length > 0 ? (
                <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
                    {filteredSpaces.map(space => (
                        <SpaceCard key={space.id} space={space} />
                    ))}
                </div>
            ) : (
                <div className="col-span-full text-center text-muted-foreground py-12 flex flex-col items-center justify-center rounded-lg border-2 border-dashed">
                     <AlertTriangle className="h-12 w-12 mb-4" />
                    <h3 className="text-xl font-bold font-headline">No Spaces Found</h3>
                    <p className="mt-2">No spaces found matching your search criteria.</p>
                </div>
            )}
        </>
    );
}
