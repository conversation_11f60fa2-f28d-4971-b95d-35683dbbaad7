
"use client";

import { useFormStatus, useFormState } from "react-dom";
import { factCheckAction } from "@/app/actions";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { useEffect } from "react";
import { useToast } from "@/hooks/use-toast";
import { Bo<PERSON>, Loader2, ShieldCheck, ShieldX, Link as LinkIcon } from "lucide-react";
import Link from "next/link";
import { Badge } from "@/components/ui/badge";

const initialState = {
  message: "",
  data: null,
  errors: null,
};

function SubmitButton() {
  const { pending } = useFormStatus();
  return (
    <Button 
      type="submit" 
      aria-disabled={pending} 
      disabled={pending}
      className="bg-accent text-accent-foreground border-2 border-black shadow-[2px_2px_0px_black] hover:shadow-[4px_4px_0px_black] active:shadow-[1px_1px_0px_black] transition-all w-full md:w-auto"
    >
      {pending ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Bot className="mr-2 h-4 w-4" />}
      {pending ? "Checking..." : "Check Fact"}
    </Button>
  );
}

export default function FactCheckerClient() {
  const [state, formAction] = useFormState(factCheckAction, initialState);
  const { toast } = useToast();

  useEffect(() => {
    if (state.message && state.message !== 'success') {
      toast({
        variant: "destructive",
        title: "Error",
        description: state.message,
      });
    }
  }, [state, toast]);

  return (
    <div className="grid gap-6">
      <Card className="border-2 border-black shadow-[4px_4px_0px_black] rounded-lg">
        <CardHeader>
          <CardTitle className="font-headline">Enter a Statement to Fact-Check</CardTitle>
          <CardDescription>
            Submit a factual claim, and our AI will verify it against authoritative sources.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form action={formAction} className="space-y-4">
            <Textarea
              name="statement"
              placeholder="e.g., 'The global population is expected to reach 10 billion by 2050.'"
              className="min-h-[100px] border-2 border-black focus:shadow-[2px_2px_0px_black] transition-shadow"
            />
             {state.errors?.statement && <p className="text-sm font-medium text-destructive">{state.errors.statement}</p>}
            <SubmitButton />
          </form>
        </CardContent>
      </Card>

      {state.data && (
        <Card className="border-2 border-black shadow-[4px_4px_0px_black] rounded-lg">
            <CardHeader>
                <CardTitle className="font-headline">Fact-Check Result</CardTitle>
                <div className="flex items-center pt-2">
                    {state.data.isAccurate ? (
                        <Badge className="bg-green-600 hover:bg-green-700 text-white">
                            <ShieldCheck className="mr-2 h-4 w-4" />
                            Accurate
                        </Badge>
                    ) : (
                        <Badge variant="destructive">
                            <ShieldX className="mr-2 h-4 w-4" />
                            Inaccurate / Misleading
                        </Badge>
                    )}
                </div>
            </CardHeader>
            <CardContent className="space-y-4">
                <div>
                    <h3 className="font-headline text-lg mb-2">Explanation</h3>
                    <p className="text-base">{state.data.explanation}</p>
                </div>
                {state.data.sources && state.data.sources.length > 0 && (
                     <div>
                        <h3 className="font-headline text-lg mb-2">Sources</h3>
                        <div className="space-y-2">
                            {state.data.sources.map((source: string, i: number) => (
                                <Link key={i} href={source} target="_blank" rel="noopener noreferrer" className="flex items-center gap-2 text-sm text-blue-600 hover:underline break-all">
                                    <LinkIcon className="h-4 w-4 flex-shrink-0" />
                                    <span>{source}</span>
                                </Link>
                            ))}
                        </div>
                    </div>
                )}
            </CardContent>
        </Card>
      )}
    </div>
  );
}
