const { Storage } = require('@google-cloud/storage');
const path = require('path');

// Initialize Google Cloud Storage
const storage = new Storage({
  projectId: 'politica-overview',
  // You may need to set up service account credentials
  // keyFilename: path.join(__dirname, 'path-to-service-account-key.json'),
});

const bucketName = 'politica-overview.appspot.com';

async function configureCORS() {
  try {
    console.log('Configuring CORS for Firebase Storage bucket...');
    
    const corsConfiguration = [
      {
        origin: [
          'http://localhost:3000',
          'http://localhost:3001', 
          'https://localhost:3000',
          'https://localhost:3001',
          'https://politica-overview.web.app',
          'https://politica-overview.firebaseapp.com'
        ],
        method: ['GET', 'POST', 'PUT', 'DELETE', 'HEAD', 'OPTIONS'],
        maxAgeSeconds: 3600,
        responseHeader: [
          'Content-Type',
          'Authorization',
          'Content-Length',
          'User-Agent',
          'X-Requested-With',
          'X-Firebase-Storage-Version',
          'X-Firebase-GMPID'
        ]
      }
    ];

    await storage.bucket(bucketName).setCorsConfiguration(corsConfiguration);
    
    console.log('✅ CORS configuration applied successfully!');
    
    // Verify the configuration
    const [metadata] = await storage.bucket(bucketName).getMetadata();
    console.log('Current CORS configuration:', JSON.stringify(metadata.cors, null, 2));
    
  } catch (error) {
    console.error('❌ Error configuring CORS:', error.message);
    
    if (error.message.includes('authentication')) {
      console.log('\n🔧 Authentication required. Please run:');
      console.log('gcloud auth application-default login');
    }
    
    if (error.message.includes('permission')) {
      console.log('\n🔧 Permission issue. Make sure you have Storage Admin role.');
    }
  }
}

// Run the configuration
configureCORS();
