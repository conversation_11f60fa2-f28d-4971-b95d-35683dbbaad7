
'use client';

import { useState, useEffect, useTransition } from 'react';
import { useFormState } from 'react-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage, FormDescription } from '@/components/ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Switch } from '@/components/ui/switch';
import { cn } from '@/lib/utils';
import { ArrowLeft, ArrowRight, Rocket, X, Tag, Loader2 } from 'lucide-react';
import { Badge } from '../ui/badge';
import type { FieldPath } from 'react-hook-form';
import { createSpaceAction } from '@/app/actions';
import { useToast } from '@/hooks/use-toast';

// Zod schema based on the detailed requirements
const spaceFormSchema = z.object({
  name: z.string().min(1, "Space name is required.").max(50, "Name cannot exceed 50 characters."),
  description: z.string().min(100, "Description must be at least 100 characters.").max(1000, "Description cannot exceed 1000 characters."),
  category: z.string().min(1, "Please select a category."),
  tags: z.array(z.string()).max(10, "You can add up to 10 tags.").default([]),
  
  contentTypes: z.object({
    text: z.boolean().default(true),
    documents: z.boolean().default(false),
    images: z.boolean().default(false),
    video: z.boolean().default(false),
    data: z.boolean().default(false),
    code: z.boolean().default(false),
    claims: z.boolean().default(false),
    polls: z.boolean().default(false),
  }).default({ text: true }),
  discussionFormat: z.enum(['threaded', 'linear', 'q&a', 'debate']).default('threaded'),
  contentApproval: z.boolean().default(false),
  versioning: z.boolean().default(false),

  tools: z.object({
    projectManagement: z.boolean().default(false),
    fileStorage: z.boolean().default(false),
    codeSandbox: z.boolean().default(false),
    whiteboard: z.boolean().default(false),
    conferencing: z.boolean().default(false),
    calendar: z.boolean().default(false),
  }).default({}),

  visibility: z.enum(['public', 'private', 'secret']).default('public'),
  publicJoinOptions: z.enum(['open', 'request']).default('open'),
  privateJoinOptions: z.enum(['request', 'invite']).default('request'),
  secretJoinOptions: z.enum(['invite', 'link']).default('invite'),
  requiredAuthLevel: z.string().default('Community'),
});

type SpaceFormValues = z.infer<typeof spaceFormSchema>;

const STEPS = [
  { id: 'Step 1', name: 'Essentials & Identity', fields: ['name', 'description', 'category', 'tags'] as const },
  { id: 'Step 2', name: 'Content & Discussion', fields: ['contentTypes', 'discussionFormat', 'contentApproval', 'versioning'] as const },
  { id: 'Step 3', name: 'Collaboration Tools', fields: ['tools'] as const },
  { id: 'Step 4', name: 'Access & Privacy', fields: ['visibility', 'publicJoinOptions', 'privateJoinOptions', 'requiredAuthLevel'] as const },
  { id: 'Step 5', name: 'Review & Launch', fields: [] as const }
];

const contentTypesOptions = [
    { id: 'text', label: 'Text Posts' },
    { id: 'documents', label: 'Documents (Upload)' },
    { id: 'images', label: 'Images (Upload)' },
    { id: 'video', label: 'Video (Upload/Embed)' },
    { id: 'data', label: 'Data Files (CSV, JSON)' },
    { id: 'code', label: 'Code Snippets' },
    { id: 'claims', label: 'Structured Claims' },
    { id: 'polls', label: 'Polls / Surveys' },
] as const;

const collaborationToolsOptions = [
    { id: 'projectManagement', label: 'Project Management Workspace' },
    { id: 'fileStorage', label: 'Shared File Storage' },
    { id: 'codeSandbox', label: 'Secure Code Sandbox' },
    { id: 'whiteboard', label: 'Whiteboard / Diagramming' },
    { id: 'conferencing', label: 'Voice/Video Channel' },
    { id: 'calendar', label: 'Shared Calendar' },
] as const;

const initialState = {
  message: '',
  errors: null,
};

export default function CreateSpaceForm() {
  const [currentStep, setCurrentStep] = useState(0);
  const [tagInput, setTagInput] = useState('');
  const [isPending, startTransition] = useTransition();
  const { toast } = useToast();
  
  const [state, formAction] = useFormState(createSpaceAction, initialState);

  useEffect(() => {
    if (state.message && !state.errors) {
      toast({ variant: 'destructive', title: 'Error Creating Space', description: state.message });
    }
  }, [state, toast]);
  
  const form = useForm<SpaceFormValues>({
    resolver: zodResolver(spaceFormSchema),
    defaultValues: {
      name: '',
      description: '',
      category: '',
      tags: [],
      contentTypes: { text: true, documents: false, images: false, video: false, data: false, code: false, claims: false, polls: false },
      discussionFormat: 'threaded',
      contentApproval: false,
      versioning: false,
      tools: { projectManagement: false, fileStorage: false, codeSandbox: false, whiteboard: false, conferencing: false, calendar: false },
      visibility: 'public',
      publicJoinOptions: 'open',
      privateJoinOptions: 'request',
      secretJoinOptions: 'invite',
      requiredAuthLevel: 'Community'
    },
  });
  
  const handleAddTag = () => {
      const currentTags = form.getValues('tags');
      if (tagInput && currentTags.length < 10 && !currentTags.includes(tagInput)) {
        form.setValue('tags', [...currentTags, tagInput]);
        setTagInput('');
      }
  };
  
  const handleRemoveTag = (tagToRemove: string) => {
      const currentTags = form.getValues('tags');
      form.setValue('tags', currentTags.filter(tag => tag !== tagToRemove));
  };


  const processForm = (data: SpaceFormValues) => {
    const formData = new FormData();
    formData.append('spaceData', JSON.stringify(data));
    startTransition(() => {
        formAction(formData);
    });
  };
  
  const next = async () => {
    const fields = STEPS[currentStep].fields;
    const output = await form.trigger(fields as FieldPath<SpaceFormValues>[], { shouldFocus: true });
    
    if (!output) return;
    
    if (currentStep < STEPS.length - 1) {
      setCurrentStep(step => step + 1);
    }
  };
  
  const prev = () => {
    if (currentStep > 0) {
      setCurrentStep(step => step - 1);
    }
  };

  const selectedVisibility = form.watch('visibility');
  const formValues = form.getValues();
  const tags = form.watch('tags');

  return (
    <Card className="w-full max-w-3xl mx-auto border-2 border-black shadow-[8px_8px_0px_#000]">
      <CardHeader>
        <CardTitle className="font-headline text-2xl">{STEPS[currentStep].name}</CardTitle>
        <CardDescription>Step {currentStep + 1} of {STEPS.length}</CardDescription>
        <Progress value={((currentStep + 1) / STEPS.length) * 100} className="w-full" />
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(processForm)} className="space-y-8" id="create-space-form">
            
            {/* Step 1 */}
            <div className={cn(currentStep !== 0 && "hidden", "space-y-4")}>
                <FormField control={form.control} name="name" render={({ field }) => (
                    <FormItem>
                        <FormLabel>Space Name</FormLabel>
                        <FormControl><Input placeholder="e.g., Quantum Computing Ethics" {...field} className="border-2 border-black" /></FormControl>
                        <FormMessage />
                    </FormItem>
                )} />
                <FormField control={form.control} name="description" render={({ field }) => (
                    <FormItem>
                        <FormLabel>Space Description</FormLabel>
                        <FormControl><Textarea placeholder="Describe the mission and purpose of this space..." {...field} className="min-h-[120px] border-2 border-black" /></FormControl>
                        <FormMessage />
                    </FormItem>
                )} />
                <FormField control={form.control} name="category" render={({ field }) => (
                    <FormItem>
                        <FormLabel>Primary Category</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                                <SelectTrigger className="border-2 border-black"><SelectValue placeholder="Select a category" /></SelectTrigger>
                            </FormControl>
                            <SelectContent className="border-2 border-black">
                                <SelectItem value="Science & Research">Science & Research</SelectItem>
                                <SelectItem value="Policy & Governance">Policy & Governance</SelectItem>
                                <SelectItem value="Technology & Innovation">Technology & Innovation</SelectItem>
                                <SelectItem value="Social Impact">Social Impact</SelectItem>
                                <SelectItem value="Arts & Culture">Arts & Culture</SelectItem>
                                <SelectItem value="Education">Education</SelectItem>
                                <SelectItem value="Business & Industry">Business & Industry</SelectItem>
                                <SelectItem value="General Discussion">General Discussion</SelectItem>
                            </SelectContent>
                        </Select>
                        <FormMessage />
                    </FormItem>
                )} />
                <FormItem>
                    <FormLabel>Tags / Keywords</FormLabel>
                    <div className="flex items-center gap-2">
                        <Input 
                            value={tagInput}
                            onChange={e => setTagInput(e.target.value)}
                            onKeyDown={e => { if (e.key === 'Enter') { e.preventDefault(); handleAddTag(); } }}
                            placeholder="Add a tag..." 
                            className="border-2 border-black"
                        />
                        <Button type="button" onClick={handleAddTag} className="border-2 border-black shadow-[2px_2px_0px_#000]">Add Tag</Button>
                    </div>
                    <div className="flex flex-wrap gap-2 mt-2">
                        {tags.map((tag, index) => (
                           <div key={index} className="flex items-center gap-1 bg-muted p-1 px-2 border-2 border-black rounded-md">
                               <Tag className="h-4 w-4" />
                               <span>{tag}</span>
                               <button type="button" onClick={() => handleRemoveTag(tag)}><X className="h-4 w-4" /></button>
                           </div>
                        ))}
                    </div>
                    <FormMessage>{form.formState.errors.tags?.message}</FormMessage>
                </FormItem>
            </div>
            
            {/* Step 2 */}
            <div className={cn(currentStep !== 1 && "hidden", "space-y-6")}>
                <div>
                    <Label className="text-base font-medium">Allowed Content Types</Label>
                    <p className="text-sm text-muted-foreground">Define what can be posted in this Space.</p>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                        {contentTypesOptions.map(item => (
                            <FormField key={item.id} control={form.control} name={`contentTypes.${item.id}`} render={({ field }) => (
                                <FormItem className="flex items-center space-x-3 space-y-0 p-3 bg-muted/50 rounded-md border-2 border-black">
                                    <FormControl><Checkbox checked={field.value} onCheckedChange={field.onChange} /></FormControl>
                                    <FormLabel className="font-normal">{item.label}</FormLabel>
                                </FormItem>
                            )} />
                        ))}
                    </div>
                </div>
                <FormField control={form.control} name="discussionFormat" render={({ field }) => (
                    <FormItem>
                        <FormLabel className="text-base font-medium">Default Discussion Format</FormLabel>
                        <FormControl>
                            <RadioGroup onValueChange={field.onChange} defaultValue={field.value} className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <FormItem className="flex items-center space-x-3 space-y-0 p-3 bg-muted/50 rounded-md border-2 border-black">
                                    <FormControl><RadioGroupItem value="threaded" /></FormControl>
                                    <FormLabel className="font-normal">Threaded Comments</FormLabel>
                                </FormItem>
                                <FormItem className="flex items-center space-x-3 space-y-0 p-3 bg-muted/50 rounded-md border-2 border-black">
                                    <FormControl><RadioGroupItem value="linear" /></FormControl>
                                    <FormLabel className="font-normal">Linear Feed</FormLabel>
                                </FormItem>
                                <FormItem className="flex items-center space-x-3 space-y-0 p-3 bg-muted/50 rounded-md border-2 border-black">
                                    <FormControl><RadioGroupItem value="q&a" /></FormControl>
                                    <FormLabel className="font-normal">Moderated Q&A</FormLabel>
                                </FormItem>
                                <FormItem className="flex items-center space-x-3 space-y-0 p-3 bg-muted/50 rounded-md border-2 border-black">
                                    <FormControl><RadioGroupItem value="debate" /></FormControl>
                                    <FormLabel className="font-normal">Formal Debate</FormLabel>
                                </FormItem>
                            </RadioGroup>
                        </FormControl>
                        <FormMessage />
                    </FormItem>
                )} />
                 <FormField control={form.control} name="contentApproval" render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border-2 border-black p-4">
                        <div className="space-y-0.5">
                            <FormLabel className="text-base">Content Approval Workflow</FormLabel>
                            <FormDescription>If enabled, all new posts require moderator approval.</FormDescription>
                        </div>
                        <FormControl><Switch checked={field.value} onCheckedChange={field.onChange} /></FormControl>
                    </FormItem>
                 )} />
                 <FormField control={form.control} name="versioning" render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border-2 border-black p-4">
                        <div className="space-y-0.5">
                            <FormLabel className="text-base">Content Versioning</FormLabel>
                            <FormDescription>If enabled, edits to content will retain a version history.</FormDescription>
                        </div>
                        <FormControl><Switch checked={field.value} onCheckedChange={field.onChange} /></FormControl>
                    </FormItem>
                 )} />
            </div>

            {/* Step 3 */}
            <div className={cn(currentStep !== 2 && "hidden", "space-y-4")}>
                <div>
                    <Label className="text-base font-medium">Collaboration Tools</Label>
                    <p className="text-sm text-muted-foreground">Select tools to activate within this Space.</p>
                </div>
                 {collaborationToolsOptions.map(tool => (
                     <FormField key={tool.id} control={form.control} name={`tools.${tool.id}`} render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border-2 border-black p-4">
                            <div className="space-y-0.5">
                                <FormLabel className="text-base">{tool.label}</FormLabel>
                            </div>
                            <FormControl><Switch checked={field.value} onCheckedChange={field.onChange} /></FormControl>
                        </FormItem>
                    )} />
                ))}
            </div>

            {/* Step 4 */}
            <div className={cn(currentStep !== 3 && "hidden", "space-y-6")}>
                <FormField control={form.control} name="visibility" render={({ field }) => (
                    <FormItem>
                        <FormLabel className="text-base font-medium">Space Visibility</FormLabel>
                        <FormControl>
                            <RadioGroup onValueChange={field.onChange} defaultValue={field.value} className="mt-2 space-y-2">
                                <FormItem className="p-4 rounded-lg border-2 border-black data-[state=checked]:bg-muted/50" data-state={field.value === 'public' ? 'checked' : 'unchecked'}>
                                    <div className="flex items-center space-x-3">
                                        <FormControl><RadioGroupItem value="public" /></FormControl>
                                        <FormLabel className="font-normal text-base">Public</FormLabel>
                                    </div>
                                    <p className="pl-8 text-sm text-muted-foreground">Discoverable by all. Content is visible to everyone.</p>
                                </FormItem>
                                <FormItem className="p-4 rounded-lg border-2 border-black data-[state=checked]:bg-muted/50" data-state={field.value === 'private' ? 'checked' : 'unchecked'}>
                                    <div className="flex items-center space-x-3">
                                        <FormControl><RadioGroupItem value="private" /></FormControl>
                                        <FormLabel className="font-normal text-base">Private</FormLabel>
                                    </div>
                                    <p className="pl-8 text-sm text-muted-foreground">Discoverable, but content is hidden from non-members.</p>
                                </FormItem>
                                <FormItem className="p-4 rounded-lg border-2 border-black data-[state=checked]:bg-muted/50" data-state={field.value === 'secret' ? 'checked' : 'unchecked'}>
                                    <div className="flex items-center space-x-3">
                                        <FormControl><RadioGroupItem value="secret" /></FormControl>
                                        <FormLabel className="font-normal text-base">Secret</FormLabel>
                                    </div>
                                    <p className="pl-8 text-sm text-muted-foreground">Not discoverable. Invite only.</p>
                                </FormItem>
                            </RadioGroup>
                        </FormControl>
                    </FormItem>
                )} />

                <div style={{ display: selectedVisibility === 'public' ? 'block' : 'none' }}>
                    <FormField control={form.control} name="publicJoinOptions" render={({ field }) => (
                        <FormItem>
                            <FormLabel className="font-medium">Public Join Options</FormLabel>
                            <FormControl>
                                <RadioGroup onValueChange={field.onChange} defaultValue={field.value} className="space-y-1 rounded-md border-2 border-black p-4 mt-2">
                                    <FormItem className="flex items-center space-x-3"><FormControl><RadioGroupItem value="open" /></FormControl><FormLabel className="font-normal">Open Join</FormLabel></FormItem>
                                    <FormItem className="flex items-center space-x-3"><FormControl><RadioGroupItem value="request" /></FormControl><FormLabel className="font-normal">Request to Join</FormLabel></FormItem>
                                </RadioGroup>
                            </FormControl>
                        </FormItem>
                    )} />
                </div>

                <div style={{ display: selectedVisibility === 'private' ? 'block' : 'none' }}>
                    <FormField control={form.control} name="privateJoinOptions" render={({ field }) => (
                        <FormItem>
                            <FormLabel className="font-medium">Private Join Options</FormLabel>
                            <FormControl>
                                <RadioGroup onValueChange={field.onChange} defaultValue={field.value} className="space-y-1 rounded-md border-2 border-black p-4 mt-2">
                                    <FormItem className="flex items-center space-x-3"><FormControl><RadioGroupItem value="request" /></FormControl><FormLabel className="font-normal">Request to Join (Mandatory)</FormLabel></FormItem>
                                    <FormItem className="flex items-center space-x-3"><FormControl><RadioGroupItem value="invite" /></FormControl><FormLabel className="font-normal">Invitation Only</FormLabel></FormItem>
                                </RadioGroup>
                            </FormControl>
                        </FormItem>
                    )} />
                </div>

                <FormField control={form.control} name="requiredAuthLevel" render={({ field }) => (
                    <FormItem>
                        <FormLabel>Required AuthLevel for Joining</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                                <SelectTrigger className="border-2 border-black"><SelectValue /></SelectTrigger>
                            </FormControl>
                            <SelectContent className="border-2 border-black">
                                <SelectItem value="Community">Community Verified</SelectItem>
                                <SelectItem value="Professional">Professionally Verified</SelectItem>
                                <SelectItem value="Institutional">Institution Verified</SelectItem>
                                <SelectItem value="Expert">Expert Verified</SelectItem>
                            </SelectContent>
                        </Select>
                        <FormMessage />
                    </FormItem>
                )} />
            </div>

            {/* Step 5 */}
            <div className={cn(currentStep !== 4 && "hidden", "space-y-4")}>
                <h3 className="text-xl font-headline">Review Your Space</h3>
                <p className="text-muted-foreground">Please review all the settings before launching your space.</p>
                <Card className="border-2 border-black">
                    <CardContent className="p-6 space-y-4">
                        <div><strong className="font-headline">Name:</strong> {formValues.name}</div>
                        <div><strong className="font-headline">Category:</strong> {formValues.category}</div>
                        <div><strong className="font-headline">Visibility:</strong> {formValues.visibility}</div>
                        <div><strong className="font-headline">Discussion Format:</strong> {formValues.discussionFormat}</div>
                        <div><strong className="font-headline">Content Approval:</strong> {formValues.contentApproval ? 'Enabled' : 'Disabled'}</div>
                        <div className="flex flex-wrap gap-2">
                            <strong className="font-headline">Enabled Tools:</strong> 
                            {Object.entries(formValues.tools).filter(([, enabled]) => enabled).map(([key]) => <Badge key={key} variant="secondary" className="border-2 border-black capitalize">{key.replace(/([A-Z])/g, ' $1')}</Badge>)}
                            {Object.values(formValues.tools).every(v => !v) && <span>None</span>}
                        </div>
                    </CardContent>
                </Card>
            </div>
          </form>
        </Form>
        
        {/* Navigation */}
        <div className="mt-8 pt-5 border-t-2 border-black flex justify-between">
          <Button type="button" onClick={prev} disabled={currentStep === 0} variant="outline" className="border-2 border-black">
            <ArrowLeft className="mr-2" /> Back
          </Button>
          
          {currentStep === STEPS.length - 1 ? (
             <Button type="submit" form="create-space-form" disabled={isPending} className="bg-accent text-accent-foreground border-2 border-black shadow-[2px_2px_0px_#000]">
                {isPending ? <Loader2 className="mr-2 animate-spin" /> : <Rocket className="mr-2" />}
                Launch Space
            </Button>
          ) : (
             <Button type="button" onClick={next} className="bg-accent text-accent-foreground border-2 border-black shadow-[2px_2px_0px_#000]">
                Next Step <ArrowRight className="ml-2" />
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
