import { Construction } from "lucide-react";

export default function PlaceholderContent({ title, description }: { title: string; description?: string }) {
  return (
    <div className="flex flex-1 items-center justify-center rounded-lg border-2 border-dashed shadow-sm h-full">
      <div className="flex flex-col items-center gap-4 text-center">
        <Construction className="h-16 w-16 text-muted-foreground" />
        <h3 className="text-2xl font-bold tracking-tight">{title}</h3>
        <p className="text-muted-foreground">{description || "This page is under construction. Check back later for updates."}</p>
      </div>
    </div>
  );
}
