
'use client';

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { LogOut, User } from "lucide-react"
import Link from "next/link"

export function UserNav() {

  const handleLogout = async () => {
    const response = await fetch('/api/logout', { method: 'POST' });
    if(response.ok) {
        window.location.assign('/');
    }
  };
  
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="relative h-auto w-full justify-start p-0 hover:bg-transparent">
            <div className="flex items-center gap-3">
                 <Avatar className="h-12 w-12 border-2 border-black">
                    <AvatarImage src="https://placehold.co/100x100" alt="@rayvlicaltandon" data-ai-hint="user avatar" />
                    <AvatarFallback>RT</AvatarFallback>
                </Avatar>
                <div className="text-left whitespace-nowrap">
                    <p className="font-bold text-sm text-foreground">RAYVICALTANDON</p>
                    <p className="text-xs text-foreground/80">New Member</p>
                </div>
            </div>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-56 border-2 border-black" align="end" forceMount>
        <DropdownMenuLabel className="font-normal">
          <div className="flex flex-col space-y-1">
            <p className="text-sm font-medium leading-none">rayvlicaltandon</p>
            <p className="text-xs leading-none text-muted-foreground">
              New Member
            </p>
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
           <DropdownMenuItem asChild>
              <Link href="/profile">
                <User className="mr-2 h-4 w-4" />
                <span>Profile</span>
              </Link>
            </DropdownMenuItem>
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={handleLogout} className="cursor-pointer">
          <LogOut className="mr-2 h-4 w-4" />
          <span>Log out</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
