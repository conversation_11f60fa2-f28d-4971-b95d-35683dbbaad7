
import AppSidebar from "@/components/app-sidebar";
import PlaceholderContent from "@/components/placeholder-content";
import { Menu } from "lucide-react";
import { Sheet, SheetContent, SheetTrigger, SheetTitle } from "@/components/ui/sheet";
import { <PERSON><PERSON> } from "@/components/ui/button";
import PageLayout from "@/components/page-layout";

function AppHeader() {
  return (
    <header className="flex h-16 items-center gap-4 border-b-2 border-black bg-background px-6">
        <Sheet>
            <SheetTrigger asChild>
              <Button
                variant="outline"
                size="icon"
                className="shrink-0 md:hidden border-2 border-black"
              >
                <Menu className="h-5 w-5" />
                <span className="sr-only">Toggle navigation menu</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="p-0 border-r-2 border-black bg-primary">
                <SheetTitle className="sr-only">Navigation Menu</SheetTitle>
                <AppSidebar />
            </SheetContent>
        </Sheet>
        <div className="text-2xl font-bold font-headline">Expert Network</div>
    </header>
  );
}

export default function ExpertNetworkPage() {
    return (
        <PageLayout>
            <AppHeader />
            <main className="flex flex-1 flex-col p-6 bg-background">
                <PlaceholderContent title="Expert Network" />
            </main>
        </PageLayout>
    );
}
