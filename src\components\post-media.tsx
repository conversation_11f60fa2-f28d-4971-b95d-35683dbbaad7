'use client';

import { useState } from 'react';
import Image from 'next/image';
import { Di<PERSON>, DialogContent, DialogTrigger } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ChevronLeft, ChevronRight, X, Download, Maximize2, Play, Video } from 'lucide-react';
import { cn } from '@/lib/utils';
import type { MediaItem } from '@/types/post';
import { formatFileSize } from '@/lib/image-compression';
import { formatVideoDuration } from '@/lib/video-processing';

interface PostMediaProps {
  media: MediaItem[];
  className?: string;
}

export default function PostMedia({ media, className }: PostMediaProps) {
  const [selectedImageIndex, setSelectedImageIndex] = useState<number | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  if (!media || media.length === 0) {
    return null;
  }

  // Separate images and videos
  const images = media.filter(item => item.type === 'image');
  const videos = media.filter(item => item.type === 'video');

  const openImageDialog = (index: number) => {
    setSelectedImageIndex(index);
    setIsDialogOpen(true);
  };

  const closeImageDialog = () => {
    setIsDialogOpen(false);
    setSelectedImageIndex(null);
  };

  const navigateImage = (direction: 'prev' | 'next') => {
    if (selectedImageIndex === null) return;
    
    if (direction === 'prev') {
      setSelectedImageIndex(selectedImageIndex > 0 ? selectedImageIndex - 1 : media.length - 1);
    } else {
      setSelectedImageIndex(selectedImageIndex < media.length - 1 ? selectedImageIndex + 1 : 0);
    }
  };

  const downloadImage = (mediaItem: MediaItem) => {
    const link = document.createElement('a');
    link.href = mediaItem.url;
    link.download = mediaItem.fileName;
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const renderMediaGrid = () => {
    const count = media.length;

    if (count === 1) {
      return (
        <div className="relative">
          <MediaImage 
            mediaItem={media[0]} 
            onClick={() => openImageDialog(0)}
            className="w-full max-h-96 object-cover rounded-lg border-2 border-black cursor-pointer hover:opacity-90 transition-opacity"
          />
        </div>
      );
    }

    if (count === 2) {
      return (
        <div className="grid grid-cols-2 gap-2">
          {media.map((item, index) => (
            <MediaImage
              key={item.id}
              mediaItem={item}
              onClick={() => openImageDialog(index)}
              className="w-full h-48 object-cover rounded-lg border-2 border-black cursor-pointer hover:opacity-90 transition-opacity"
            />
          ))}
        </div>
      );
    }

    if (count === 3) {
      return (
        <div className="grid grid-cols-2 gap-2">
          <MediaImage
            mediaItem={media[0]}
            onClick={() => openImageDialog(0)}
            className="w-full h-96 object-cover rounded-lg border-2 border-black cursor-pointer hover:opacity-90 transition-opacity"
          />
          <div className="grid grid-rows-2 gap-2">
            <MediaImage
              mediaItem={media[1]}
              onClick={() => openImageDialog(1)}
              className="w-full h-[11.5rem] object-cover rounded-lg border-2 border-black cursor-pointer hover:opacity-90 transition-opacity"
            />
            <MediaImage
              mediaItem={media[2]}
              onClick={() => openImageDialog(2)}
              className="w-full h-[11.5rem] object-cover rounded-lg border-2 border-black cursor-pointer hover:opacity-90 transition-opacity"
            />
          </div>
        </div>
      );
    }

    // 4 or more images
    return (
      <div className="grid grid-cols-2 gap-2">
        {media.slice(0, 3).map((item, index) => (
          <MediaImage
            key={item.id}
            mediaItem={item}
            onClick={() => openImageDialog(index)}
            className={cn(
              "w-full object-cover rounded-lg border-2 border-black cursor-pointer hover:opacity-90 transition-opacity",
              index === 0 ? "h-96 col-span-1 row-span-2" : "h-[11.5rem]"
            )}
          />
        ))}
        {count > 3 && (
          <div 
            className="relative w-full h-[11.5rem] rounded-lg border-2 border-black cursor-pointer hover:opacity-90 transition-opacity"
            onClick={() => openImageDialog(3)}
          >
            <MediaImage
              mediaItem={media[3]}
              className="w-full h-full object-cover rounded-lg"
            />
            <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center rounded-lg">
              <span className="text-white text-xl font-bold">+{count - 3}</span>
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <>
      <div className={cn("mt-3", className)}>
        {renderMediaGrid()}
      </div>

      {/* Image Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-4xl w-full h-[90vh] p-0 border-2 border-black">
          {selectedImageIndex !== null && (
            <div className="relative w-full h-full flex flex-col">
              {/* Header */}
              <div className="flex items-center justify-between p-4 border-b-2 border-black">
                <div className="flex items-center space-x-2">
                  <Badge variant="outline">
                    {selectedImageIndex + 1} of {media.length}
                  </Badge>
                  <span className="text-sm text-gray-600">
                    {media[selectedImageIndex].fileName}
                  </span>
                  <Badge variant="secondary" className="text-xs">
                    {formatFileSize(media[selectedImageIndex].fileSize)}
                  </Badge>
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => downloadImage(media[selectedImageIndex])}
                  >
                    <Download className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={closeImageDialog}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Image */}
              <div className="flex-1 relative flex items-center justify-center bg-gray-50">
                <Image
                  src={media[selectedImageIndex].url}
                  alt={media[selectedImageIndex].fileName}
                  width={media[selectedImageIndex].width || 800}
                  height={media[selectedImageIndex].height || 600}
                  className="max-w-full max-h-full object-contain"
                />

                {/* Navigation */}
                {media.length > 1 && (
                  <>
                    <Button
                      variant="outline"
                      size="sm"
                      className="absolute left-4 top-1/2 transform -translate-y-1/2"
                      onClick={() => navigateImage('prev')}
                    >
                      <ChevronLeft className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="absolute right-4 top-1/2 transform -translate-y-1/2"
                      onClick={() => navigateImage('next')}
                    >
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </>
                )}
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
}

interface MediaImageProps {
  mediaItem: MediaItem;
  onClick?: () => void;
  className?: string;
}

function MediaImage({ mediaItem, onClick, className }: MediaImageProps) {
  if (mediaItem.type === 'video') {
    return (
      <div className={cn("relative", className)} onClick={onClick}>
        {/* Video thumbnail or poster */}
        {mediaItem.thumbnailUrl ? (
          <Image
            src={mediaItem.thumbnailUrl}
            alt={`${mediaItem.fileName} thumbnail`}
            width={mediaItem.width || 400}
            height={mediaItem.height || 300}
            className={cn("w-full h-full object-cover", className)}
          />
        ) : (
          <div className="w-full h-full bg-gray-900 flex items-center justify-center">
            <Video className="h-12 w-12 text-white" />
          </div>
        )}

        {/* Video overlay */}
        <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center">
          <div className="bg-black bg-opacity-50 rounded-full p-3">
            <Play className="h-6 w-6 text-white fill-white" />
          </div>
        </div>

        {/* Video info badges */}
        <div className="absolute top-2 right-2 space-y-1">
          <Badge variant="secondary" className="text-xs">
            🎥 {mediaItem.duration ? formatVideoDuration(mediaItem.duration) : 'Video'}
          </Badge>
          <Badge variant="secondary" className="text-xs">
            {formatFileSize(mediaItem.fileSize)}
          </Badge>
        </div>
      </div>
    );
  }

  // Image rendering (existing logic)
  return (
    <div className={cn("relative", className)} onClick={onClick}>
      <Image
        src={mediaItem.url}
        alt={mediaItem.fileName}
        width={mediaItem.width || 400}
        height={mediaItem.height || 300}
        className={cn("w-full h-full object-cover", className)}
      />
      {onClick && (
        <div className="absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity bg-black bg-opacity-20">
          <Maximize2 className="h-6 w-6 text-white" />
        </div>
      )}
    </div>
  );
}
