
'use client';

import { createContext, useContext, useState, type ReactNode, Dispatch, SetStateAction } from 'react';

interface ScrollContextProps {
    isScrolled: boolean;
    setIsScrolled: Dispatch<SetStateAction<boolean>>;
}

const ScrollContext = createContext<ScrollContextProps | undefined>(undefined);

export const ScrollProvider = ({ children }: { children: ReactNode }) => {
    const [isScrolled, setIsScrolled] = useState(false);

    return (
        <ScrollContext.Provider value={{ isScrolled, setIsScrolled }}>
            {children}
        </ScrollContext.Provider>
    );
};

export const useScrollContext = () => {
    const context = useContext(ScrollContext);
    if (!context) {
        throw new Error('useScrollContext must be used within a ScrollProvider');
    }
    return context;
};
