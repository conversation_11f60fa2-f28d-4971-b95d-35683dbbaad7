
'use client';

import { useState, useEffect } from 'react';
import FeedPost from '@/components/feed-post';
import { Card, CardContent } from './ui/card';
import { Button } from './ui/button';
import type { Post } from '@/types/post';
import { AlertTriangle, Loader2 } from 'lucide-react';
import { onAuthStateChanged, type User } from 'firebase/auth';
import { auth } from '@/lib/firebase';
import { cn } from '@/lib/utils';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';

const spaces = ['All Spaces', 'Professional', 'Politics', 'Science', 'Forum'];

export default function Feed({ initialPosts }: { initialPosts: Post[] }) {
  const [posts, setPosts] = useState<Post[]>(initialPosts);
  const [currentUser, setCurrentUser] = useState<User | null>(auth.currentUser);
  const [authChecked, setAuthChecked] = useState(false);
  
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const activeSpace = searchParams.get('space') || 'All Spaces';

  useEffect(() => {
    setPosts(initialPosts);
  }, [initialPosts]);

  useEffect(() => {
    const unsubscribeAuth = onAuthStateChanged(auth, (user) => {
      setCurrentUser(user);
      setAuthChecked(true);
    });
    return () => unsubscribeAuth();
  }, []);

  const handleSpaceChange = (space: string) => {
    const params = new URLSearchParams(searchParams);
    if (space === 'All Spaces') {
        params.delete('space');
    } else {
        params.set('space', space);
    }
    router.push(`${pathname}?${params.toString()}`, { scroll: false });
  };
  
  const handleAction = () => {
    router.refresh();
  }

  const renderContent = () => {
    if (!authChecked) {
      return (
        <div className="flex justify-center items-center p-8">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        </div>
      );
    }

    if (!currentUser) {
      return (
        <div className="flex flex-col items-center justify-center text-center text-muted-foreground p-8 rounded-lg border-2 border-dashed">
            <AlertTriangle className="h-12 w-12 mb-4" />
            <h3 className="text-xl font-bold font-headline">Welcome to POLITICA</h3>
            <p className="mt-2">Please sign in or create an account to view the feed and join the conversation.</p>
        </div>
      );
    }
    
    if (posts.length > 0) {
      return (
        <div className="space-y-4">
          {posts.map((post) => (
            <FeedPost key={post.id} post={post} currentUserId={currentUser?.uid || null} onAction={handleAction}/>
          ))}
        </div>
      );
    }

    return (
       <div className="flex flex-col items-center justify-center text-center text-muted-foreground p-8 rounded-lg border-2 border-dashed">
          <AlertTriangle className="h-12 w-12 mb-4" />
          <h3 className="text-xl font-bold font-headline">The Feed is Quiet</h3>
          <p className="mt-2">There are no posts here yet. Be the first to start a conversation by creating a post!</p>
      </div>
    );
  };

  return (
    <Card className="border-2 border-black rounded-lg shadow-[4px_4px_0px_black]">
      <CardContent className="p-0">
        <div className="sticky top-0 z-10 bg-card p-4 border-b-2 border-black rounded-t-lg">
            <div className="flex items-center gap-2">
                {spaces.map(space => (
                    <Button 
                        key={space}
                        onClick={() => handleSpaceChange(space)}
                        variant={activeSpace === space ? 'default' : 'secondary'}
                        className={cn(
                            "border-2 border-black shadow-[2px_2px_0px_#000]",
                            activeSpace === space && "bg-accent text-accent-foreground"
                        )}
                    >
                        {space}
                    </Button>
                ))}
            </div>
        </div>
        <div className="p-4">
          {renderContent()}
        </div>
      </CardContent>
    </Card>
  );
}
