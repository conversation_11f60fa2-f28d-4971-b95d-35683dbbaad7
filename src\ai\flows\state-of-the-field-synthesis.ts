'use server';

/**
 * @fileOverview Synthesizes the state of the field for a given topic.
 *
 * - stateOfTheFieldSynthesis - A function that summarizes and analyzes the current state of research on a topic.
 * - StateOfTheFieldSynthesisInput - The input type for the stateOfTheFieldSynthesis function.
 * - StateOfTheFieldSynthesisOutput - The return type for the stateOfTheFieldSynthesis function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const StateOfTheFieldSynthesisInputSchema = z.object({
  topic: z.string().describe('The research topic to synthesize.'),
});
export type StateOfTheFieldSynthesisInput = z.infer<typeof StateOfTheFieldSynthesisInputSchema>;

const StateOfTheFieldSynthesisOutputSchema = z.object({
  summary: z.string().describe('A summary of the current state of research on the topic.'),
  keyTrends: z.string().describe('Key trends in the research field.'),
  gaps: z.string().describe('Identified gaps in the research.'),
  controversies: z.string().describe('Controversies in the research field.'),
});
export type StateOfTheFieldSynthesisOutput = z.infer<typeof StateOfTheFieldSynthesisOutputSchema>;

export async function stateOfTheFieldSynthesis(
  input: StateOfTheFieldSynthesisInput
): Promise<StateOfTheFieldSynthesisOutput> {
  return stateOfTheFieldSynthesisFlow(input);
}

const prompt = ai.definePrompt({
  name: 'stateOfTheFieldSynthesisPrompt',
  input: {schema: StateOfTheFieldSynthesisInputSchema},
  output: {schema: StateOfTheFieldSynthesisOutputSchema},
  prompt: `You are an expert researcher tasked with summarizing the current state of a research field.

  Analyze the current state of research on the following topic, identify key trends, gaps, and controversies to efficiently contextualize the work.

  Topic: {{{topic}}}

  Ensure your response is a valid JSON object that conforms to the specified output schema.
  `,
});

const stateOfTheFieldSynthesisFlow = ai.defineFlow(
  {
    name: 'stateOfTheFieldSynthesisFlow',
    inputSchema: StateOfTheFieldSynthesisInputSchema,
    outputSchema: StateOfTheFieldSynthesisOutputSchema,
  },
  async input => {
    const {output} = await prompt(input);
    return output!;
  }
);
